#!/usr/bin/env python3
"""
Script para buscar registros con cambios de nombres reales
"""

import pandas as pd
import json
import os

def find_name_changes():
    """Buscar registros que realmente tengan cambios de nombres"""
    print("🔍 BÚSQUEDA: Registros con cambios de nombres...")
    
    parquet_file = "../S3_LOG_USER/output/20250610/LOG_USR.parquet"
    
    if not os.path.exists(parquet_file):
        print(f"❌ Archivo no encontrado: {parquet_file}")
        return
        
    try:
        # Cargar datos
        df = pd.read_parquet(parquet_file)
        print(f"📊 Registros cargados: {len(df):,}")
        
        # Buscar registros donde NOMBRE y NNOMBRE sean diferentes (cambios de nombre)
        name_changes = df[
            (df['NOMBRE'].notna()) & 
            (df['NNOMBRE'].notna()) & 
            (df['NOMBRE'] != df['NNOMBRE']) &
            (df['NOMBRE'] != '') &
            (df['NNOMBRE'] != '')
        ]
        
        print(f"📊 Registros con cambios NOMBRE->NNOMBRE: {len(name_changes):,}")
        
        if len(name_changes) > 0:
            for idx, row in name_changes.head(3).iterrows():
                print(f"\n🔍 === CAMBIO DE NOMBRE {idx} ===")
                print(f"USERHISTID: {row['USERHISTID']}")
                print(f"REQUESTTYPE: {row['REQUESTTYPE']}")
                print(f"BANKDOMAIN: {row['BANKDOMAIN']}")
                print(f"NOMBRE: {row['NOMBRE']}")
                print(f"NNOMBRE: {row['NNOMBRE']}")
                print(f"APELLIDO: {row['APELLIDO']}")
                print(f"NAPELLIDO: {row['NAPELLIDO']}")
                
                # Analizar JSON si existe
                if pd.notna(row['OLDDATA']) and row['OLDDATA']:
                    try:
                        old_data = json.loads(row['OLDDATA'])
                        print(f"OLDDATA keys: {list(old_data.keys())}")
                        
                        # Buscar firstName y lastName en todo el JSON
                        def find_names_in_json(data, path=""):
                            results = []
                            if isinstance(data, dict):
                                for key, value in data.items():
                                    if key in ['firstName', 'lastName', 'nombre', 'apellido']:
                                        results.append(f"{path}.{key}: {value}")
                                    else:
                                        results.extend(find_names_in_json(value, f"{path}.{key}"))
                            elif isinstance(data, list):
                                for i, item in enumerate(data):
                                    results.extend(find_names_in_json(item, f"{path}[{i}]"))
                            return results
                        
                        names_in_json = find_names_in_json(old_data)
                        if names_in_json:
                            print(f"Nombres en JSON: {names_in_json}")
                        else:
                            print("No se encontraron nombres en JSON")
                            
                    except Exception as e:
                        print(f"Error parseando JSON: {e}")
                        
        # Buscar registros User Modification con JSON que contengan firstName o lastName
        print(f"\n🔍 Buscando JSON con firstName/lastName...")
        json_with_names = []
        
        df_with_json = df[
            (df['REQUESTTYPE'] == 'User Modification') &
            (df['OLDDATA'].notna()) & 
            (df['OLDDATA'] != '')
        ]
        
        for idx, row in df_with_json.head(100).iterrows():  # Revisar primeros 100
            try:
                old_data = json.loads(row['OLDDATA'])
                old_json_str = str(old_data).lower()
                
                if 'firstname' in old_json_str or 'lastname' in old_json_str or 'nombre' in old_json_str:
                    json_with_names.append((idx, row, old_data))
                    
            except:
                continue
                
        print(f"📊 JSON con nombres encontrados: {len(json_with_names)}")
        
        for idx, (df_idx, row, old_data) in enumerate(json_with_names[:3]):
            print(f"\n🔍 === JSON CON NOMBRES {idx} ===")
            print(f"USERHISTID: {row['USERHISTID']}")
            print(f"NOMBRE: {row['NOMBRE']}")
            print(f"APELLIDO: {row['APELLIDO']}")
            print(f"JSON: {old_data}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    find_name_changes()
