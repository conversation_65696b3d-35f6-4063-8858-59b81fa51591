#!/usr/bin/env python3
"""
Script para debuggear específicamente el problema de CNOMBRE con "None, None"
"""

import pandas as pd
import json
import os

def debug_cnombre_issue():
    """Debuggea el problema específico de CNOMBRE que recibe 'None, None'"""
    print("🔍 DEBUGGING: Problema de CNOMBRE con 'None, None'...")
    
    parquet_file = "../S3_LOG_USER/output/20250610/LOG_USR.parquet"
    
    if not os.path.exists(parquet_file):
        print(f"❌ Archivo no encontrado: {parquet_file}")
        return
        
    try:
        # Cargar datos
        df = pd.read_parquet(parquet_file)
        print(f"📊 Registros cargados: {len(df):,}")
        
        # Buscar específicamente el registro UM.10 que sabemos tiene nombres
        target_record = df[df['USERHISTID'] == 'UM.10']
        
        if len(target_record) > 0:
            print(f"\n🎯 === REGISTRO UM.10 ENCONTRADO ===")
            record = target_record.iloc[0]
            print(f"USERHISTID: {record['USERHISTID']}")
            print(f"NOMBRE: {record['NOMBRE']}")
            print(f"APELLIDO: {record['APELLIDO']}")
            print(f"REQUESTTYPE: {record['REQUESTTYPE']}")
            
            # Analizar OLD/NEW DATA
            if pd.notna(record['OLDDATA']) and record['OLDDATA']:
                try:
                    old_data = json.loads(record['OLDDATA'])
                    print(f"\n📋 OLD DATA JSON estructura:")
                    print(f"  firstName: {old_data.get('profileDetails', {}).get('firstName', 'NO ENCONTRADO')}")
                    print(f"  lastName: {old_data.get('profileDetails', {}).get('lastName', 'NO ENCONTRADO')}")
                    
                    # Simular la extracción como lo hace el código real
                    profile_details = old_data.get('profileDetails', {})
                    first_name = profile_details.get('firstName')
                    last_name = profile_details.get('lastName')
                    
                    print(f"\n🔍 EXTRACCIÓN SIMULADA:")
                    print(f"  firstName extraído: '{first_name}'")
                    print(f"  lastName extraído: '{last_name}'")
                    
                    # Simular concatenación como firstName lastName
                    if first_name and last_name:
                        concatenated = f"{first_name} {last_name}"
                        print(f"  Concatenación esperada: '{concatenated}'")
                    else:
                        print(f"  ⚠️ PROBLEMA: Al menos uno de los valores es None")
                        print(f"    firstName is None: {first_name is None}")
                        print(f"    lastName is None: {last_name is None}")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Error parseando OLD DATA JSON: {e}")
            
            if pd.notna(record['NEWDATA']) and record['NEWDATA']:
                try:
                    new_data = json.loads(record['NEWDATA'])
                    profile_details = new_data.get('profileDetails', {})
                    first_name = profile_details.get('firstName')
                    last_name = profile_details.get('lastName')
                    
                    print(f"\n📋 NEW DATA JSON firstName/lastName:")
                    print(f"  firstName: '{first_name}'")
                    print(f"  lastName: '{last_name}'")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Error parseando NEW DATA JSON: {e}")
        else:
            print("❌ Registro UM.10 no encontrado")
            
        # Buscar otros registros CNOMBRE para ver el patrón
        print(f"\n🔍 === ANÁLISIS DE OTROS REGISTROS CNOMBRE ===")
        
        # Filtrar registros que deberían ser CNOMBRE (tienen nombres reales)
        name_records = df[
            (df['NOMBRE'].notna()) & 
            (df['APELLIDO'].notna()) & 
            (df['NOMBRE'] != 'N/A') & 
            (df['APELLIDO'] != 'N/A') &
            (df['NOMBRE'] != '') & 
            (df['APELLIDO'] != '')
        ].head(5)
        
        print(f"📊 Registros con nombres reales encontrados: {len(name_records)}")
        
        for idx, row in name_records.iterrows():
            print(f"\n--- Registro {row['USERHISTID']} ---")
            print(f"NOMBRE: {row['NOMBRE']}")
            print(f"APELLIDO: {row['APELLIDO']}")
            
            # Analizar JSON para buscar firstName/lastName
            if pd.notna(row['OLDDATA']) and row['OLDDATA']:
                try:
                    old_data = json.loads(row['OLDDATA'])
                    profile_details = old_data.get('profileDetails', {})
                    first_name = profile_details.get('firstName')
                    last_name = profile_details.get('lastName')
                    
                    print(f"  OLD firstName: '{first_name}'")
                    print(f"  OLD lastName: '{last_name}'")
                    
                    if first_name and last_name:
                        expected_old_value = f"{first_name} {last_name}"
                        print(f"  ✅ Esperado old_value: '{expected_old_value}'")
                    else:
                        print(f"  ⚠️ Uno de los valores es None en OLD")
                        
                except json.JSONDecodeError:
                    print(f"  ❌ Error parseando OLD JSON")
            
            if pd.notna(row['NEWDATA']) and row['NEWDATA']:
                try:
                    new_data = json.loads(row['NEWDATA'])
                    profile_details = new_data.get('profileDetails', {})
                    first_name = profile_details.get('firstName')
                    last_name = profile_details.get('lastName')
                    
                    print(f"  NEW firstName: '{first_name}'")
                    print(f"  NEW lastName: '{last_name}'")
                    
                    if first_name and last_name:
                        expected_new_value = f"{first_name} {last_name}"
                        print(f"  ✅ Esperado new_value: '{expected_new_value}'")
                    else:
                        print(f"  ⚠️ Uno de los valores es None en NEW")
                        
                except json.JSONDecodeError:
                    print(f"  ❌ Error parseando NEW JSON")
                    
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cnombre_issue()
