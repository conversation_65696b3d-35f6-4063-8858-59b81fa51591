#!/usr/bin/env python3
"""
Script de debug para analizar la extracción de JSON y identificar por qué los old_value llegan como 'None, None'
"""

import pandas as pd
import json
import os
import sys

def debug_json_extraction():
    """Debug para analizar la extracción de JSON"""
    print("🔍 DEBUG: Analizando extracción de JSON...")
    
    # Usar archivo conocido con datos
    parquet_file = "../S3_LOG_USER/output/20250610/LOG_USR.parquet"
    
    if not os.path.exists(parquet_file):
        print(f"❌ Archivo no encontrado: {parquet_file}")
        return
    print(f"📁 Analizando archivo: {parquet_file}")
    
    try:
        # Cargar datos
        df = pd.read_parquet(parquet_file)
        print(f"📊 Registros cargados: {len(df):,}")
        
        # Filtrar por registros que tengan OLDDATA y NEWDATA
        df_with_json = df[
            (df['REQUESTTYPE'] == 'User Modification') &
            (df['OLDDATA'].notna()) & 
            (df['OLDDATA'] != '') &
            (df['NEWDATA'].notna()) & 
            (df['NEWDATA'] != '')
        ]
        
        print(f"📊 Registros con JSON: {len(df_with_json):,}")
        
        if len(df_with_json) == 0:
            print("❌ No hay registros con JSON para analizar")
            return
            
        # Analizar el primer registro con JSON
        for idx, row in df_with_json.head(3).iterrows():
            print(f"\n🔍 === ANÁLISIS REGISTRO {idx} ===")
            print(f"USERHISTID: {row['USERHISTID']}")
            print(f"REQUESTTYPE: {row['REQUESTTYPE']}")
            print(f"BANKDOMAIN: {row['BANKDOMAIN']}")
            
            # Analizar OLDDATA
            old_data_raw = row['OLDDATA']
            print(f"\n📄 OLDDATA (raw):")
            print(f"Tipo: {type(old_data_raw)}")
            print(f"Contenido: {str(old_data_raw)[:500]}...")
            
            try:
                old_data = json.loads(old_data_raw) if old_data_raw else {}
                print(f"\n✅ OLDDATA parseado exitosamente")
                print(f"Claves principales: {list(old_data.keys()) if isinstance(old_data, dict) else 'No es dict'}")
                
                # Buscar firstName y lastName en diferentes niveles
                print(f"\n🔍 Búsqueda de nombres:")
                
                # Nivel raíz
                firstName_root = old_data.get('firstName')
                lastName_root = old_data.get('lastName')
                print(f"Nivel raíz - firstName: {firstName_root}, lastName: {lastName_root}")
                
                # Dentro de profileDetails (si existe)
                profile_details = old_data.get('profileDetails', {})
                if isinstance(profile_details, dict):
                    firstName_profile = profile_details.get('firstName')
                    lastName_profile = profile_details.get('lastName')
                    print(f"profileDetails - firstName: {firstName_profile}, lastName: {lastName_profile}")
                elif isinstance(profile_details, list) and len(profile_details) > 0:
                    firstName_profile = profile_details[0].get('firstName') if isinstance(profile_details[0], dict) else None
                    lastName_profile = profile_details[0].get('lastName') if isinstance(profile_details[0], dict) else None
                    print(f"profileDetails[0] - firstName: {firstName_profile}, lastName: {lastName_profile}")
                
                # Buscar en todos los niveles recursivamente
                def find_names_recursive(data, path=""):
                    results = []
                    if isinstance(data, dict):
                        for key, value in data.items():
                            if key == 'firstName':
                                results.append(f"{path}.{key}: {value}")
                            elif key == 'lastName':
                                results.append(f"{path}.{key}: {value}")
                            else:
                                results.extend(find_names_recursive(value, f"{path}.{key}"))
                    elif isinstance(data, list):
                        for i, item in enumerate(data):
                            results.extend(find_names_recursive(item, f"{path}[{i}]"))
                    return results
                
                names_found = find_names_recursive(old_data)
                print(f"\n🔍 Nombres encontrados recursivamente:")
                for name in names_found:
                    print(f"  {name}")
                    
            except Exception as e:
                print(f"❌ Error parseando OLDDATA: {e}")
            
            # Analizar NEWDATA
            new_data_raw = row['NEWDATA']
            print(f"\n📄 NEWDATA (raw):")
            print(f"Tipo: {type(new_data_raw)}")
            print(f"Contenido: {str(new_data_raw)[:500]}...")
            
            try:
                new_data = json.loads(new_data_raw) if new_data_raw else {}
                print(f"\n✅ NEWDATA parseado exitosamente")
                print(f"Claves principales: {list(new_data.keys()) if isinstance(new_data, dict) else 'No es dict'}")
                
                # Buscar nombres en NEWDATA también
                names_found_new = find_names_recursive(new_data)
                print(f"\n🔍 Nombres encontrados en NEWDATA:")
                for name in names_found_new:
                    print(f"  {name}")
                    
            except Exception as e:
                print(f"❌ Error parseando NEWDATA: {e}")
                
            print(f"\n" + "="*80)
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_json_extraction()
