#!/usr/bin/env python3

import pandas as pd
import json
import sys
import os

# Agregar el path para importar las funciones
sys.path.append('/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/LOGICA/REPORTES/REPORTE_LOG_USUARIO')
from postprocesar_log_usuario import LogUsuariosPostProcessor

def debug_cnombre_processing():
    """Debug específico para el procesamiento de CNOMBRE"""
    
    print("🔍 DEBUG: Analizando procesamiento de CNOMBRE...")
    
    # Cargar datos del parquet original
    parquet_path = "/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER/output/20250610/LOG_USR.parquet"
    
    if not os.path.exists(parquet_path):
        print(f"❌ No se encontró {parquet_path}")
        return
    
    df = pd.read_parquet(parquet_path)
    print(f"📊 Datos cargados: {len(df):,} registros")
    
    # Filtrar solo registros User Modification que contienen firstName/lastName
    user_mod_df = df[
        (df['REQUESTTYPE'] == 'User Modification') &
        (df['OLDDATA'].notna()) &
        (df['NEWDATA'].notna()) &
        (df['OLDDATA'].str.contains('firstName', na=False)) |
        (df['NEWDATA'].str.contains('firstName', na=False))
    ].head(20)
    
    print(f"📊 Registros User Modification con firstName: {len(user_mod_df)}")
    
    if len(user_mod_df) == 0:
        print("❌ No se encontraron registros con firstName/lastName")
        return
    
    # Crear instancia del procesador
    processor = LogUsuariosPostProcessor()
    
    print("\n🔍 Analizando registros individuales:")
    for idx, row in user_mod_df.iterrows():
        userhistid = row['USERHISTID']
        old_data = row['OLDDATA']
        new_data = row['NEWDATA']
        
        print(f"\n📝 USERHISTID: {userhistid}")
        print(f"📝 OLD_DATA: {old_data[:200]}...")
        print(f"📝 NEW_DATA: {new_data[:200]}...")
        
        try:
            # Parsear JSON
            old_json = json.loads(old_data) if old_data else {}
            new_json = json.loads(new_data) if new_data else {}
            
            # Buscar firstName y lastName de forma recursiva
            def find_names_recursive(data, path=""):
                names = {}
                if isinstance(data, dict):
                    for key, value in data.items():
                        if key == 'firstName':
                            names['firstName'] = value
                        elif key == 'lastName':
                            names['lastName'] = value
                        elif isinstance(value, (dict, list)):
                            nested_names = find_names_recursive(value, f"{path}.{key}")
                            names.update(nested_names)
                elif isinstance(data, list):
                    for i, item in enumerate(data):
                        nested_names = find_names_recursive(item, f"{path}[{i}]")
                        names.update(nested_names)
                return names
            
            old_names = find_names_recursive(old_json)
            new_names = find_names_recursive(new_json)
            
            print(f"📝 OLD JSON firstName (recursivo): {old_names.get('firstName', 'NOT_FOUND')}")
            print(f"📝 OLD JSON lastName (recursivo): {old_names.get('lastName', 'NOT_FOUND')}")
            print(f"📝 NEW JSON firstName (recursivo): {new_names.get('firstName', 'NOT_FOUND')}")
            print(f"📝 NEW JSON lastName (recursivo): {new_names.get('lastName', 'NOT_FOUND')}")
            
            # Usar extract_json_changes del procesador
            changes = processor.extract_json_changes(old_json, new_json)
            
            print(f"📝 CAMBIOS EXTRAÍDOS: {len(changes)}")
            for change in changes:
                if change['field'] == 'CNOMBRE':
                    print(f"  🎯 CNOMBRE: old='{change['old_value']}', new='{change['new_value']}'")
                    
        except Exception as e:
            print(f"❌ Error procesando JSON: {e}")
            
        if idx >= 3:  # Solo analizar primeros 3 registros
            break

if __name__ == '__main__':
    debug_cnombre_processing()
