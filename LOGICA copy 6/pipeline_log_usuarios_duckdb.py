#!/usr/bin/env python3
"""
Pipeline ETL LOG_USUARIOS Modernizado - DuckDB Único con Conversiones de Perfiles
Migración completa de Oracle a Parquet usando solo DuckDB
Incluye funcionalidad de conversión de perfiles usando conv_perfil.csv
Autor: Ingeniero de Datos
Fecha: 2025-06-05
Actualizado: 2025-06-05 - Integración de conversiones de perfiles
"""

import duckdb
import boto3
import sys
import os
import pandas as pd
import json
import secrets
import time
import re
from datetime import datetime, timedelta
from pathlib import Path
import logging

class LogUsuariosPipeline:
    def __init__(self):
        self.setup_logging()
        self.conn = duckdb.connect()
        self.setup_s3_credentials()
        self.setup_directories()
        
        # Configuración de rutas S3
        self.s3_bucket = "prd-datalake-silver-zone-637423440311"
        self.s3_sources = {
            'user_profile': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet',
            'user_identifier': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet',
            'kyc_details': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet',
            'issuer_details': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/ISSUER_DETAILS_ORA/consolidado_puro.parquet',
            'mtx_categories': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/MTX_CATEGORIES_ORA/consolidado_puro.parquet',
            'channel_grades': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/CHANNEL_GRADES_ORA/consolidado_puro.parquet',
            'user_modification_history': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet',
            'user_auth_change_history': f's3://{self.s3_bucket}/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet',
            'mtx_wallet': f's3://{self.s3_bucket}/PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet',
            'user_account_history': f's3://{self.s3_bucket}/APP_BIM_PROD_1/USER_ACCOUNT_HISTORY/consolidado_puro.parquet'
        }

        # Ruta del archivo de conversión de perfiles - AHORA LOCAL EN S3_LOG_USER
        self.conv_perfil_path = "conv_perfil.csv"

        # Lista completa de bancos del sistema (EXACTA como flujo original)
        # INCLUIR NULL para registros sin BANKDOMAIN (como Oracle)
        self.bancos_sistema = [
            '0231FCONFIANZA', 'BNACION', 'CCUSCO', 'CRANDES',
            'FCOMPARTAMOS', 'FCONFIANZA', None  # NULL para registros sin BANKDOMAIN
        ]

        # NOTA: Oracle mantiene REQUESTTYPE originales, NO los convierte a tipos específicos
        # Los tipos válidos son los que vienen directamente de Oracle
        self.oracle_request_types = [
            'CHANGE_AUTH_FACTOR', 'User Modification', 'ActivateUser', 'AfiliaUser',
            'ClosedUserAccount', 'Delete User', 'ClosedAccount', 'Lock Wallet',
            'Resume User', 'Suspend User'
        ]

        # Mapeo de tipos de transacción (EXACTO como flujo original líneas 276-294)
        self.tipos_transaccion_map = {
            "AFILIA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "PerfilA", "IdiomaA", "TelcoA", "Initiating User", "ID USUARIO", "TipoDocumentoA", "TipoDocumentoB", "NumDocumentoA", "NumDocumentoB"
            ],
            "ACTIVA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoCuenta", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Initiating User", "ID USUARIO", "ID CUENTA", "PerfilCuenta"
            ],
            "BLOQUSR": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID USUARIO"
            ],
            "BLOQCTA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID CUENTA", "PerfilCuenta"
            ],
            "DESBLCTA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID CUENTA", "PerfilCuenta"
            ],
            "DESBUSR": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID USUARIO"
            ],
            "CCEL": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "MSISDNB", "ID USUARIO"
            ],
            "CTELCO": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "TelcoA", "TelcoB", "Razon", "Initiating User", "ID USUARIO"
            ],
            "CPIN": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Initiating User", "ID USUARIO"
            ],
            "RPIN": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Initiating User", "ID USUARIO"
            ],
            "CCUENTA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID CUENTA", "PerfilCuenta"
            ],
            "CUSR": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID USUARIO"
            ],
            "CPERFIL": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "PerfilA", "PerfilB", "Razon", "Initiating User", "ID USUARIO"
            ],
            "CIDIOMA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "IdiomaA", "IdiomaB", "Razon", "Initiating User", "ID USUARIO"
            ],
            "CNOMBRE": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Nombres", "Apellidos", "Razon", "Initiating User", "NNombre", "NApellido", "ID USUARIO"
            ],
            "AGRCTA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoCuenta", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID USUARIO", "ID CUENTA", "PerfilCuenta"
            ],
            "CPCTA": [
                "TipoTransaccion", "TransactionID", "DiaHora", "TipoDocumento", "Documento", "MSISDN", "BankDomain", "Razon", "Initiating User", "ID USUARIO", "ID CUENTA", "PerfilCuentaA", "PerfilCuentaB"
            ]
        }
    
    def setup_logging(self):
        """Configura el sistema de logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('pipeline_log_usuarios.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LogUsuariosPipeline')
    
    def setup_s3_credentials(self):
        """Configura credenciales S3 en DuckDB"""
        try:
            self.logger.info("Configurando credenciales S3...")
            
            # Obtener credenciales activas de AWS CLI
            session = boto3.Session()
            credentials = session.get_credentials().get_frozen_credentials()
            
            # Cargar soporte para S3 en DuckDB
            self.conn.sql("INSTALL httpfs;")
            self.conn.sql("LOAD httpfs;")
            self.conn.sql("SET s3_region='us-east-1';")
            self.conn.sql("SET s3_use_ssl=true;")
            self.conn.sql("SET s3_url_style='path';")
            
            # Pasar credenciales explícitamente
            self.conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
            self.conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
            if credentials.token:
                self.conn.sql(f"SET s3_session_token='{credentials.token}';")
            
            self.logger.info("Credenciales S3 configuradas exitosamente")
            
        except Exception as e:
            self.logger.error(f"Error configurando credenciales S3: {e}")
            raise
    
    def setup_directories(self):
        """Crea directorios necesarios en la carpeta S3_LOG_USER"""
        base_path = Path('/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER')
        directories = ['TEMP_LOGS_USUARIOS', 'output', 'logs']
        for directory in directories:
            (base_path / directory).mkdir(parents=True, exist_ok=True)

        # Cambiar directorio de trabajo a S3_LOG_USER
        import os
        os.chdir(base_path)

    # ==========================================
    # FUNCIONES EXACTAS DEL FLUJO ORIGINAL
    # ==========================================

    def compare_data(self, old_data, new_data, parent_key=''):
        """
        Función para comparar dos objetos (pueden ser diccionarios o listas) de manera recursiva
        EXACTA del flujo original líneas 33-58
        """
        differences = []

        # Si los dos datos son diccionarios, comparamos cada clave
        if isinstance(old_data, dict) and isinstance(new_data, dict):
            for key in old_data.keys() | new_data.keys():
                new_key = f"{parent_key}.{key}" if parent_key else key
                differences.extend(self.compare_data(old_data.get(key), new_data.get(key), new_key))

        # Si los dos datos son listas, comparamos cada elemento
        elif isinstance(old_data, list) and isinstance(new_data, list):
            for idx, (old_item, new_item) in enumerate(zip(old_data, new_data)):
                new_key = f"{parent_key}[{idx}]"
                differences.extend(self.compare_data(old_item, new_item, new_key))

        # Si son diferentes en valor
        elif old_data != new_data:
            differences.append({
                "campo": parent_key,
                "old_value": old_data,
                "new_value": new_data
            })

        return differences

    def get_final_key(self, key):
        """
        Función para extraer la última clave
        EXACTA del flujo original líneas 62-64
        """
        key = re.sub(r'\[\d*\]', '', key)  # Eliminar índices de lista
        return key.split('.')[-1]

    def handle_request_type(self, request_type):
        """
        Función para manejar valores especiales según REQUESTTYPE
        EXACTA del flujo original líneas 67-91
        """
        if request_type == "Suspend User":
            return "BLOQUSR"
        elif request_type == "Lock Wallet":
            return "BLOQCTA"
        elif request_type == "Unlock Wallet":
            return "DESBLCTA"
        elif request_type == "Resume User":
            return "DESBUSR"
        elif request_type == "CHANGE_AUTH_FACTOR":
            return "CPIN"
        elif request_type == "RESET_AUTH_VALUE":
            return "RPIN"
        elif request_type == "ActivateUser":
            return "ACTIVA"
        elif request_type == "ActivateCuenta":
            return "AGRCTA"
        elif request_type == "AfiliaUser":
            return "AFILIA"
        elif request_type == "ClosedUserAccount":
            return "CUSR"
        elif request_type == "ClosedAccount":
            return "CCUENTA"
        else:
            return None

    def map_modified_field(self, key):
        """
        Función para asignar valores al campo 'campo modificado'
        EXACTA del flujo original líneas 94-103
        """
        field_map = {
            'firstName': 'CNOMBRE',
            'lastName': 'CNOMBRE',
            'attr1': 'CTELCO',
            'mobileNumber': 'CCEL',
            'preferredLanguage': 'CIDIOMA',
            'marketingProfileId': 'CPCTA'
        }
        return field_map.get(key, key)

    def create_json_from_values(self, modified_field_str, old_value_str):
        """
        Función para crear el diccionario en formato JSON
        EXACTA del flujo original líneas 141-147
        """
        # Separar las cadenas por comas
        fields = [field.strip() for field in modified_field_str.split(',')]
        values = [value.strip() for value in old_value_str.split(',')]

        # Crear el diccionario de campos: valores
        return {fields[i]: values[i] for i in range(len(fields))}

    def id_generator_by_date(self, date_str: str) -> str:
        """
        Función que genera los id de transacción para los registros
        EXACTA del flujo original líneas 260-265
        """
        date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S.%f")
        timestamp_actual = int(date_obj.timestamp() * (10 ** 5))
        random_numbers = secrets.randbelow(10)
        identifier = str(timestamp_actual) + str(random_numbers).zfill(1)
        return str(identifier)

    def id_generator_by_reference(self) -> str:
        """
        Función para generar ID de referencia
        EXACTA del flujo original líneas 267-272
        """
        generated_ref = self.id_generator_by_date("2025-03-15 00:00:00.000001")
        generated_now = self.id_generator_by_date(datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
        difference = int(generated_now) - int(generated_ref)
        time.sleep(0.015)
        return str(difference)

    def procesar_transaccion(self, df, tipo_transaccion):
        """
        Función para filtrar y procesar cada tipo de transacción
        EXACTA del flujo original líneas 276-294
        """
        # Verificar si el tipo de transacción está en el diccionario
        if tipo_transaccion not in self.tipos_transaccion_map:
            return df  # Si el tipo no está mapeado, simplemente devolvemos el DataFrame original

        # Obtener las columnas relevantes para este tipo de transacción
        columnas_relevantes = self.tipos_transaccion_map[tipo_transaccion]

        # Filtrar el DataFrame para este tipo de transacción
        df_filtrado = df[df['TipoTransaccion'] == tipo_transaccion].copy()

        # Para las columnas no incluidas, asignar None (null)
        todas_columnas = df_filtrado.columns
        for col in todas_columnas:
            if col not in columnas_relevantes:
                df_filtrado[col] = None

        # Asegurarse de que las columnas estén en el orden correcto
        df_filtrado = df_filtrado[columnas_relevantes]

        # Si el tipo de transacción es 'CNOMBRE', intercambiar los valores de NNombre <-> Nombre y NApellido <-> Apellido
        if tipo_transaccion == "CNOMBRE":
            if "NNombre" in df_filtrado.columns and "Nombres" in df_filtrado.columns:
                df_filtrado["Nombres"], df_filtrado["NNombre"] = "", df_filtrado["Nombres"]
            if "NApellido" in df_filtrado.columns and "Apellidos" in df_filtrado.columns:
                df_filtrado["Apellidos"], df_filtrado["NApellido"] = df_filtrado["Documento"].astype(str), df_filtrado["Apellidos"]

        return df_filtrado
    
    def log_execution_status(self, part: str, status: str):
        """Registra el estado de ejecución"""
        with open("execution_status.log", "a") as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"{timestamp} - {part}: {status}\n")
        self.logger.info(f"{part}: {status}")

    def load_conv_perfil_table(self):
        """
        Carga la tabla de conversión de perfiles conv_perfil.csv en DuckDB
        Replica la funcionalidad del flujo original
        """
        try:
            self.logger.info("Cargando tabla de conversión de perfiles...")

            if not Path(self.conv_perfil_path).exists():
                self.logger.warning(f"Archivo conv_perfil.csv no encontrado en {self.conv_perfil_path}")
                return False

            # Cargar CSV con pandas
            conv_perfil_df = pd.read_csv(self.conv_perfil_path)
            self.logger.info(f"Tabla conv_perfil.csv cargada: {len(conv_perfil_df)} registros")

            # Registrar DataFrame en DuckDB
            self.conn.register('conv_perfil_table', conv_perfil_df)

            # Verificar columnas esperadas
            expected_columns = ['AUTHZ_PRO_CODE', 'SEC_PRO_CODE', 'MKT_PRO_CODE', 'CATEGORY_NAME', 'MARKETING_PROFILE_NAME']
            actual_columns = list(conv_perfil_df.columns)

            if not all(col in actual_columns for col in expected_columns):
                self.logger.warning(f"Columnas esperadas: {expected_columns}")
                self.logger.warning(f"Columnas encontradas: {actual_columns}")

            self.logger.info("Tabla de conversión de perfiles cargada exitosamente en DuckDB")
            return True

        except Exception as e:
            self.logger.error(f"Error cargando tabla de conversión de perfiles: {e}")
            return False
    
    def extract_user_account_history(self, fecha: str, date_folder: str) -> str:
        """
        Extrae y filtra USER_ACCOUNT_HISTORY desde S3
        Equivalente a mysql_extract.py
        """
        try:
            self.logger.info(f"Extrayendo USER_ACCOUNT_HISTORY para fecha: {fecha}")
            
            # Query para filtrar datos por fecha
            query = f"""
            SELECT 
                USER_ID,
                ACCOUNT_ID,
                CREATED_AT,
                ATTR7_OLD,
                ATTR8_OLD,
                CATEGORY_OLD,
                ISSUER_OLD,
                GRADE_OLD
            FROM read_parquet('{self.s3_sources['user_account_history']}')
            WHERE CAST(CREATED_AT AS DATE) >= CAST('{fecha}' AS DATE)
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_ACCOUNT_HISTORY.parquet"
            Path(f"TEMP_LOGS_USUARIOS/{date_folder}").mkdir(parents=True, exist_ok=True)
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"USER_ACCOUNT_HISTORY extraído: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error extrayendo USER_ACCOUNT_HISTORY: {e}")
            raise

    def create_user_id_mapping(self, fecha: str, date_folder: str) -> str:
        """
        NUEVO MÉTODO: Crea mapeo de USER_IDs por MSISDN
        Resuelve el problema de múltiples USER_IDs para el mismo usuario
        """
        try:
            self.logger.info(f"Creando mapeo de USER_IDs por MSISDN para fecha: {fecha}")

            # Query para crear mapeo completo
            query = f"""
            CREATE OR REPLACE TABLE user_id_mapping AS
            WITH all_user_ids AS (
                -- USER_IDs de USER_PROFILE
                SELECT DISTINCT USER_ID, MSISDN, CREATED_ON, 'USER_PROFILE' as source
                FROM read_parquet('{self.s3_sources['user_profile']}')
                WHERE MSISDN IS NOT NULL AND USER_ID IS NOT NULL

                UNION ALL

                -- USER_IDs de MTX_WALLET
                SELECT DISTINCT USER_ID, MSISDN, CREATED_ON, 'MTX_WALLET' as source
                FROM read_parquet('{self.s3_sources['mtx_wallet']}')
                WHERE MSISDN IS NOT NULL AND USER_ID IS NOT NULL
            ),
            msisdn_groups AS (
                SELECT
                    MSISDN,
                    USER_ID,
                    CREATED_ON,
                    source,
                    ROW_NUMBER() OVER (PARTITION BY MSISDN ORDER BY CREATED_ON ASC) as user_order,
                    COUNT(*) OVER (PARTITION BY MSISDN) as total_users
                FROM all_user_ids
            )
            SELECT
                MSISDN,
                USER_ID,
                CREATED_ON,
                source,
                user_order,
                total_users,
                -- USER_ID principal (el más antiguo)
                FIRST_VALUE(USER_ID) OVER (PARTITION BY MSISDN ORDER BY CREATED_ON ASC) as primary_user_id,
                -- USER_ID más reciente
                LAST_VALUE(USER_ID) OVER (PARTITION BY MSISDN ORDER BY CREATED_ON ASC
                                          ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as latest_user_id
            FROM msisdn_groups
            """

            self.conn.execute(query)

            # Guardar mapeo
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_ID_MAPPING.parquet"
            self.conn.execute(f"COPY user_id_mapping TO '{output_path}' (FORMAT PARQUET);")

            # Verificar registros
            count_result = self.conn.execute("SELECT COUNT(*) FROM user_id_mapping").fetchone()
            record_count = count_result[0] if count_result else 0

            # Log estadísticas del mapeo
            stats_result = self.conn.execute("""
            SELECT
                COUNT(DISTINCT MSISDN) as unique_msisdns,
                COUNT(*) as total_user_ids,
                COUNT(CASE WHEN total_users > 1 THEN 1 END) as multiple_user_ids
            FROM user_id_mapping
            """).fetchone()

            unique_msisdns, total_user_ids, multiple_user_ids = stats_result

            self.logger.info(f"Mapeo de USER_IDs creado: {record_count} registros -> {output_path}")
            self.logger.info(f"Estadísticas: {unique_msisdns} MSISDNs únicos, {total_user_ids} USER_IDs totales, {multiple_user_ids} con múltiples USER_IDs")

            return output_path

        except Exception as e:
            self.logger.error(f"Error creando mapeo de USER_IDs: {e}")
            raise

    def process_sp_pre_log_usr(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_PRE_LOG_USR usando DuckDB - VERSIÓN CORREGIDA
        Crea USER_DATA_TRX.parquet con datos consolidados
        Incluye mapeo de USER_IDs para manejar múltiples USER_IDs por MSISDN
        """
        try:
            self.logger.info(f"Ejecutando SP_PRE_LOG_USR CORREGIDO (incluye USER_IDs múltiples) para fecha: {fecha}")

            # Query SQL CORREGIDA - incluye TODOS los USER_IDs por MSISDN como Oracle
            # Usar solo columnas que realmente existen
            query = f"""
            WITH WALLETS AS (
                SELECT
                    MW.USER_ID,
                    COALESCE(MW.WALLET_NUMBER, 'N/A') AS WALLET_NUMBER,
                    COALESCE(MW.ISSUER_ID, 0) AS ISSUER_ID,
                    COALESCE(MW.USER_GRADE, 'DEFAULT') AS USER_GRADE,
                    ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY COALESCE(MW.MODIFIED_ON, MW.CREATED_ON, '1900-01-01') DESC) AS ORDEN
                FROM read_parquet('{self.s3_sources['mtx_wallet']}') MW
                WHERE MW.USER_ID IS NOT NULL
            )
            SELECT
                CASE 
                    WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
                    ELSE UP.USER_ID
                END AS USER_ID,
                UP.USER_ID AS O_USER_ID,
                CASE 
                    WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
                    WHEN LENGTH(REPLACE(UP.USER_ID,'US.','')) > 15 THEN SUBSTR(REPLACE(UP.USER_ID,'US.',''), -15)
                    ELSE REPLACE(UP.USER_ID,'US.','') 
                END AS USER_ID_M,
                COALESCE(UK.ID_TYPE, 'N/A') AS ID_TYPE,
                COALESCE(UK.ID_VALUE, UP.USER_CODE, UP.USER_ID) AS ID_VALUE,
                -- LÓGICA CORREGIDA PARA WALLET_NUMBER (igual que Oracle SP_PRE_LOG_USR)
                CASE 
                    WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
                    WHEN LENGTH(REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', '')) > 15 
                        THEN SUBSTR(REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', ''), -15)
                    ELSE REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', '')
                END AS WALLET_NUMBER,
                UP.STATUS,
                -- GRADE_NAME igual que Oracle
                CASE 
                    WHEN UP.MSISDN IN ('***********','***********','***********','***********') 
                        THEN REPLACE(REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144',''),'0231','') || ' MERCHANT GENERAL ACCOUNT PROFILE'
                    WHEN UP.MSISDN IN ('***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********') 
                        THEN UPPER(COALESCE(CG.GRADE_NAME, MW.USER_GRADE, '')) || ' PROFILE'
                    ELSE REPLACE(REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144',''),'0231','') || ' ' || UPPER(COALESCE(CG.GRADE_NAME, MW.USER_GRADE, ''))
                END AS GRADE_NAME,
                COALESCE(UP.MSISDN, '') AS MSISDN,
                COALESCE(UP.CREATED_ON, '1900-01-01') AS CREATED_ON,
                COALESCE(UP.CREATED_BY, 'SYSTEM') AS CREATED_BY,
                COALESCE(UP.REMARKS, '') AS REMARKS,
                UP.MODIFIED_ON AS STATUS_CHANGE_ON,
                REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144','') AS ISSUER_CODE,
                COALESCE(UP.FIRST_NAME, UP.USER_CODE, 'N/A') AS FIRST_NAME,
                COALESCE(UP.LAST_NAME, UP.USER_CODE, 'N/A') AS LAST_NAME,
                COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User') AS CATEGORY_NAME,
                -- PROFILE igual que Oracle
                CASE 
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Final User' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'USUARIO FINAL'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'BIMER User' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'BIMER'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Virtual Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENTE VIRTUAL'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENTE'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agencia' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENCIA'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Super Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'SUPER AGENTE'
                    WHEN UPPER(COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID)) = 'REMESAS WU' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'COMERCIO'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Dispersor' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'SUPER AGENTE'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Biller' THEN 
                        CASE 
                            WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'COMERCIO'
                            ELSE CASE
                                WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'ENTEL PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                                ELSE COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                            END 
                        END
                    ELSE COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                END AS PROFILE,
                -- PROFILE_TRX igual que Oracle
                CASE 
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Final User' THEN 'USUARIO FINAL'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'BIMER User' THEN 'BIMER'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Virtual Agent' THEN 'AGENTE VIRTUAL'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agent' THEN 'AGENTE'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agencia' THEN 'AGENCIA'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Super Agent' THEN 'SUPER AGENTE'
                    WHEN UPPER(COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID)) = 'REMESAS WU' THEN 'COMERCIO'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Dispersor' THEN 'SUPER AGENTE'
                    WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Biller' THEN 
                        CASE 
                            WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'COMERCIO'
                            ELSE CASE
                                WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'ENTEL PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                                WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                                ELSE COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                            END 
                        END
                    ELSE COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                END AS PROFILE_TRX,
                COALESCE(UP.ATTR1, '') AS ATTR1,
                COALESCE(UP.PREFERRED_LANG, 'ES') AS PREFERRED_LANG,
                COALESCE(UP.USER_CODE, UP.USER_ID) AS USER_CODE,
                COALESCE(UP.LOGIN_ID, UP.USER_ID) AS LOGIN_ID,
                COALESCE(CAST(UP.WORKSPACE_ID AS VARCHAR), '1') AS WORKSPACE_ID
            FROM read_parquet('{self.s3_sources['user_profile']}') UP
            INNER JOIN read_parquet('{self.s3_sources['kyc_details']}') UK ON UP.KYC_ID = UK.KYC_ID
            LEFT JOIN WALLETS MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN = 1
            LEFT JOIN read_parquet('{self.s3_sources['issuer_details']}') ID ON MW.ISSUER_ID = ID.ISSUER_ID
            LEFT JOIN read_parquet('{self.s3_sources['mtx_categories']}') MC ON CAST(UP.CATEGORY_ID AS VARCHAR) = CAST(MC.CATEGORY_CODE AS VARCHAR)
            LEFT JOIN read_parquet('{self.s3_sources['channel_grades']}') CG ON CAST(MW.USER_GRADE AS VARCHAR) = CAST(CG.GRADE_CODE AS VARCHAR)
            WHERE UP.USER_ID IS NOT NULL
                AND CAST(UP.CREATED_ON AS DATE) <= CAST('{fecha}' AS DATE)
                -- CORRECCIÓN: Incluye TODOS los USER_IDs creados hasta la fecha (como Oracle)
                -- Esto permite capturar USER_IDs múltiples del mismo MSISDN
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_DATA_TRX.parquet"
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"SP_PRE_LOG_USR completado: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error en SP_PRE_LOG_USR: {e}")
            raise
    
    def process_sp_user_modification(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_USER_MODIFICATION usando DuckDB
        Crea USER_MODIFICATION_DAY.parquet
        """
        try:
            self.logger.info(f"Ejecutando SP_USER_MODIFICATION para fecha: {fecha}")
            
            # Query SQL equivalente al SP_USER_MODIFICATION original
            query = f"""
            SELECT 
                umh.REQUEST_TYPE,
                umh.old_data, 
                umh.new_data,
                json_extract_string(umh.new_data, '$.profileDetails.remarks') AS razon,
                umh.user_id,
                umh.created_by,
                umh.created_on
            FROM read_parquet('{self.s3_sources['user_modification_history']}') umh
            WHERE CAST(umh.CREATED_ON AS DATE) = CAST('{fecha}' AS DATE)
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_MODIFICATION_DAY.parquet"
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"SP_USER_MODIFICATION completado: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error en SP_USER_MODIFICATION: {e}")
            raise
    
    def process_sp_user_auth_day(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_USER_AUTH_DAY usando DuckDB
        Crea USER_AUTH_CHANGE_HISTORY.parquet
        """
        try:
            self.logger.info(f"Ejecutando SP_USER_AUTH_DAY para fecha: {fecha}")
            
            # Query SQL equivalente al SP_USER_AUTH_DAY original
            query = f"""
            SELECT 
                uach.MODIFIED_ON,
                uach.MODIFICATION_TYPE,
                uach.MODIFIED_BY,
                uach.AUTHENTICATION_ID
            FROM read_parquet('{self.s3_sources['user_auth_change_history']}') uach
            WHERE CAST(uach.MODIFIED_ON AS DATE) = CAST('{fecha}' AS DATE)
            AND uach.AUTHENTICATION_TYPE = 'PIN'
            """
            
            # Guardar resultado temporal
            output_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_AUTH_CHANGE_HISTORY.parquet"
            
            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)
            
            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0
            
            self.logger.info(f"SP_USER_AUTH_DAY completado: {record_count} registros -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error en SP_USER_AUTH_DAY: {e}")
            raise

    def process_sp_log_usr(self, fecha: str, date_folder: str) -> str:
        """
        Procesa SP_LOG_USR usando DuckDB - VERSIÓN EXACTA AL ORIGINAL
        Replica línea por línea el SP_LOG_USR.sql de Oracle
        """
        try:
            self.logger.info(f"Ejecutando SP_LOG_USR EXACTO (igual a Oracle) para fecha: {fecha}")
            
            # Rutas de archivos temporales
            user_modification_day_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_MODIFICATION_DAY.parquet"
            user_data_trx_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_DATA_TRX.parquet"
            user_auth_change_path = f"TEMP_LOGS_USUARIOS/{date_folder}/USER_AUTH_CHANGE_HISTORY.parquet"
            
            # Crear WALLET_OLD temporal (EXACTO como Oracle)
            wallet_old_path = f"TEMP_LOGS_USUARIOS/{date_folder}/WALLET_OLD.parquet"
            
            wallet_old_query = f"""
            CREATE TABLE wallet_old_temp AS
            SELECT 
                UD.USER_ID,
                UD.ATTR7_OLD,
                UD.ATTR8_OLD,
                UD.CREATED_AT,
                ID.ISSUER_CODE,
                UD.GRADE_OLD AS GRADE_NAME_OLD,
                ROW_NUMBER() OVER(PARTITION BY UD.USER_ID ORDER BY UD.CREATED_AT DESC) AS ORDEN
            FROM read_parquet('{self.s3_sources['user_account_history']}') UD 
            INNER JOIN read_parquet('{self.s3_sources['issuer_details']}') ID ON UD.ISSUER_OLD = ID.ISSUER_ID
            """
            
            self.conn.execute(wallet_old_query)
            
            # Guardar WALLET_OLD
            self.conn.execute(f"COPY wallet_old_temp TO '{wallet_old_path}' (FORMAT PARQUET);")

            # LOGGING DETALLADO PARA DEBUG DEL JOIN USER_IDENTIFIER
            try:
                # Contar registros en cada tabla para debug
                auth_count = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{user_auth_change_path}') WHERE MODIFICATION_TYPE = 'CHANGE_AUTH_FACTOR'").fetchone()[0]
                self.logger.info(f"  🔍 DEBUG: CHANGE_AUTH_FACTOR total: {auth_count:,} registros")

                identifier_count = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{self.s3_sources['user_identifier']}')").fetchone()[0]
                self.logger.info(f"  🔍 DEBUG: USER_IDENTIFIER total: {identifier_count:,} registros")

                # Verificar JOIN específico para nuestro caso de prueba
                test_auth_id = "AU.8511734046727622"
                test_user_id = "US.8368734046721007"

                test_join_auth = self.conn.execute(f"""
                    SELECT COUNT(*) FROM read_parquet('{self.s3_sources['user_identifier']}')
                    WHERE AUTHENTICATION_ID = '{test_auth_id}'
                """).fetchone()[0]
                self.logger.info(f"  🔍 DEBUG: USER_IDENTIFIER con {test_auth_id}: {test_join_auth} registros")

                if test_join_auth > 0:
                    test_join_user = self.conn.execute(f"""
                        SELECT USER_ID FROM read_parquet('{self.s3_sources['user_identifier']}')
                        WHERE AUTHENTICATION_ID = '{test_auth_id}' LIMIT 1
                    """).fetchone()
                    if test_join_user:
                        self.logger.info(f"  🔍 DEBUG: USER_ID encontrado: {test_join_user[0]}")
                        if test_join_user[0] == test_user_id:
                            self.logger.info(f"  ✅ DEBUG: JOIN debería funcionar correctamente")
                        else:
                            self.logger.warning(f"  ❌ DEBUG: USER_ID no coincide. Esperado: {test_user_id}, Encontrado: {test_join_user[0]}")
                else:
                    self.logger.warning(f"  ❌ DEBUG: AUTHENTICATION_ID {test_auth_id} NO encontrado en USER_IDENTIFIER")

            except Exception as e:
                self.logger.warning(f"Error en logging detallado: {e}")

            # Query principal - EXACTO como SP_LOG_USR.sql línea por línea
            query = f"""
            WITH
            WALLET_OLD AS (
                SELECT 
                    USER_ID,
                    ATTR7_OLD,
                    ATTR8_OLD,
                    CREATED_AT,
                    ISSUER_CODE,
                    GRADE_NAME_OLD,
                    ORDEN
                FROM read_parquet('{wallet_old_path}')
            ),
            PROCESS AS (
                -- PARTE 1: User Modifications (EXACTO como Oracle líneas 47-77)
                SELECT
                    'UM.' || CAST(ROW_NUMBER() OVER() AS VARCHAR) AS userHistId,
                    umh.created_on AS createdOn,
                    ud.ID_TYPE AS TipoDocumento,
                    ud.ID_VALUE AS Documento,
                    ud.MSISDN AS Msisdn,
                    NULL AS MsisdnB,
                    ud.ISSUER_CODE AS BankDomain,
                    CASE
                        WHEN UMH.REQUEST_TYPE IN ('Resume User','Unlock Wallet') THEN 'ID:awspdp/ADMIN'
                        ELSE REPLACE(REPLACE(umh.created_by,'US.',''),'SELF','ID:unknown/SERVICE')
                    END AS created_by,
                    ud.USER_ID_M AS userId,
                    'MOBILE_MONEY' AS accountType,
                    ud.WALLET_NUMBER AS accountId,
                    ud.FIRST_NAME AS Nombre,
                    ud.LAST_NAME AS Apellido,
                    NULL AS NNombre,
                    NULL AS NApellido,
                    REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
                    NULL AS perfilB,
                    ud.PREFERRED_LANG AS IdiomaA,
                    NULL AS IdiomaB,
                    ud.ATTR1 AS TelcoA,
                    NULL AS TelcoB,
                    umh.razon,
                    ud.grade_name as PerfilCuenta,
                    ud.grade_name as PerfilCuentaA,
                    NULL AS perfilCuentaB,
                    ud.ID_TYPE AS TipoDocumentoA,
                    NULL AS TipoDocumentoB,
                    ud.ID_VALUE AS DocumentoB,
                    NULL AS NumDocumentoB,
                    umh.request_type AS requestType,
                    umh.old_data AS oldData,
                    umh.new_data AS newData,
                    UD.O_USER_ID
                FROM read_parquet('{user_modification_day_path}') umh
                INNER JOIN read_parquet('{user_data_trx_path}') ud ON umh.user_id = ud.O_USER_ID
                
                UNION ALL
                
                -- PARTE 2: User Auth Change History (EXACTO como Oracle líneas 79-110)
                SELECT
                    uach.AUTHENTICATION_ID AS userHistId,
                    uach.MODIFIED_ON AS createdOn,
                    COALESCE(ud.ID_TYPE, '') AS TipoDocumento,
                    COALESCE(ud.ID_VALUE, '') AS Documento,
                    COALESCE(ud.MSISDN, '') AS Msisdn,
                    NULL AS MsisdnB,
                    CASE WHEN COALESCE(ud.ISSUER_CODE, 'DEFAULT') = 'DEFAULT' THEN NULL ELSE ud.ISSUER_CODE END AS BankDomain,
                    CASE
                        WHEN UACH.MODIFICATION_TYPE = 'RESET_AUTH_VALUE' THEN 'ID:unknown/SERVICE'
                        ELSE REPLACE(REPLACE(uach.MODIFIED_BY,'US.',''),'SELF','ID:unknown/SERVICE')
                    END AS created_by,
                    COALESCE(ud.USER_ID_M, '') AS userId,
                    'MOBILE_MONEY' AS accountType,
                    COALESCE(ud.WALLET_NUMBER, '') AS accountId,
                    COALESCE(ud.FIRST_NAME, '') AS Nombre,
                    COALESCE(ud.LAST_NAME, '') AS Apellido,
                    NULL AS NNombre,
                    NULL AS NApellido,
                    REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
                    NULL AS perfilB,
                    COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
                    NULL AS IdiomaB,
                    COALESCE(ud.ATTR1, '') AS TelcoA,
                    NULL AS TelcoB,
                    NULL AS Razon,
                    COALESCE(ud.grade_name, '') as PerfilCuenta,
                    COALESCE(ud.grade_name, '') as PerfilCuentaA,
                    NULL AS perfilCuentaB,
                    COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
                    NULL AS TipoDocumentoB,
                    COALESCE(ud.ID_VALUE, '') AS DocumentoB,
                    NULL AS NumDocumentoB,
                    uach.MODIFICATION_TYPE AS requestType,
                    NULL AS oldData,
                    NULL AS newData,
                    COALESCE(UD.O_USER_ID, uach.AUTHENTICATION_ID) AS O_USER_ID
                FROM read_parquet('{user_auth_change_path}') uach
                LEFT JOIN read_parquet('{self.s3_sources['user_identifier']}') ui ON uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID
                LEFT JOIN read_parquet('{user_data_trx_path}') ud ON ui.USER_ID = ud.O_USER_ID
                WHERE ui.USER_ID IS NOT NULL AND ud.O_USER_ID IS NOT NULL
                
                UNION ALL
                
                -- PARTE 3: ActivateUser + AfiliaUser (EXACTO como Oracle líneas 112-140)
                SELECT
                    ud.O_USER_ID AS userHistId,
                    ud.CREATED_ON AS createdOn,
                    CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE ud.ID_TYPE END AS TipoDocumento,
                    CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE ud.ID_VALUE END AS Documento,
                    ud.MSISDN AS Msisdn,
                    NULL AS MsisdnB,
                    CASE WHEN ud.ISSUER_CODE = 'DEFAULT' THEN NULL ELSE ud.ISSUER_CODE END AS BankDomain,
                    REPLACE(REPLACE(ud.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
                    ud.USER_ID_M AS userId,
                    'MOBILE_MONEY' AS accountType,
                    ud.WALLET_NUMBER AS accountId,
                    ud.FIRST_NAME AS Nombre,
                    ud.LAST_NAME AS Apellido,
                    NULL AS NNombre,
                    NULL AS NApellido,
                    REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
                    NULL AS perfilB,
                    ud.PREFERRED_LANG AS IdiomaA,
                    NULL AS IdiomaB,
                    ud.ATTR1 AS TelcoA,
                    NULL AS TelcoB,
                    NULL AS Razon,
                    CASE WHEN ud.grade_name LIKE '%GENERAL%' THEN REPLACE(ud.grade_name,'NORMAL GENERAL','NORMAL') ELSE ud.grade_name END as PerfilCuenta,
                    CASE WHEN ud.grade_name LIKE '%GENERAL%' THEN REPLACE(ud.grade_name,'NORMAL GENERAL','NORMAL') ELSE ud.grade_name END as PerfilCuentaA,
                    NULL AS perfilCuentaB,
                    ud.ID_TYPE AS TipoDocumentoA,
                    NULL AS TipoDocumentoB,
                    ud.ID_VALUE AS DocumentoB,
                    NULL AS NumDocumentoB,
                    req.requestType AS requestType,
                    NULL AS oldData,
                    NULL AS newData,
                    UD.O_USER_ID
                FROM read_parquet('{user_data_trx_path}') ud
                CROSS JOIN (SELECT 'ActivateUser' AS requestType FROM (VALUES (1)) AS t(x)
                            UNION ALL
                            SELECT 'AfiliaUser' FROM (VALUES (1)) AS t(x)) req
                WHERE CAST(ud.CREATED_ON AS DATE) = CAST('{fecha}' AS DATE)
                
                UNION ALL
                
                -- PARTE 4: ClosedAccount + ClosedUserAccount (EXACTO como Oracle líneas 142-170)
                SELECT 
                    ud.O_USER_ID AS userHistId,
                    ud.STATUS_CHANGE_ON AS createdOn,
                    ud.ID_TYPE AS TipoDocumento,
                    ud.ID_VALUE || 'X' || ud.USER_ID_M AS Documento,
                    ud.MSISDN AS Msisdn,
                    NULL AS MsisdnB,
                    CASE WHEN ud.ISSUER_CODE = 'DEFAULT' THEN NULL ELSE ud.ISSUER_CODE END AS BankDomain,
                    REPLACE(REPLACE(ud.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
                    ud.USER_ID_M AS userId,
                    'MOBILE_MONEY' AS accountType,
                    ud.WALLET_NUMBER AS accountId,
                    ud.FIRST_NAME AS Nombre,
                    ud.LAST_NAME AS Apellido,
                    NULL AS NNombre,
                    NULL AS NApellido,
                    REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
                    NULL AS perfilB,
                    ud.PREFERRED_LANG AS IdiomaA,
                    NULL AS IdiomaB,
                    ud.ATTR1 AS TelcoA,
                    NULL AS TelcoB,
                    ud.remarks AS Razon,
                    ud.grade_name as PerfilCuenta,
                    ud.grade_name as PerfilCuentaA,
                    NULL AS perfilCuentaB,
                    ud.ID_TYPE AS TipoDocumentoA,
                    NULL AS TipoDocumentoB,
                    ud.ID_VALUE AS DocumentoB,
                    NULL AS NumDocumentoB,
                    req.requestType AS requestType,
                    NULL AS oldData, 
                    NULL AS newData,
                    UD.O_USER_ID
                FROM read_parquet('{user_data_trx_path}') ud
                CROSS JOIN (SELECT 'ClosedAccount' AS requestType FROM (VALUES (1)) AS t(x)
                            UNION ALL 
                            SELECT 'ClosedUserAccount' FROM (VALUES (1)) AS t(x)) req
                WHERE ud.STATUS = 'N'
                AND CAST(ud.STATUS_CHANGE_ON AS DATE) = CAST('{fecha}' AS DATE)
            )
            -- SELECT FINAL (EXACTO como Oracle líneas 172-206) - COLUMNAS EN MAYÚSCULAS
            SELECT
                P.userHistId AS USERHISTID,
                P.createdOn AS CREATEDON,
                P.TipoDocumento AS TIPODOCUMENTO,
                P.Documento AS DOCUMENTO,
                P.Msisdn AS MSISDN,
                P.MsisdnB AS MSISDNB,
                CASE
                    WHEN DA.USER_ID IS NOT NULL THEN
                        CASE WHEN DA.ISSUER_CODE = 'DEFAULT' THEN NULL ELSE DA.ISSUER_CODE END
                    ELSE P.BankDomain
                END AS BANKDOMAIN,
                P.created_by AS CREATED_BY,
                CASE
                    WHEN DA.USER_ID IS NOT NULL THEN DA.ATTR7_OLD
                    ELSE P.userId
                END AS USERID,
                P.accountType AS ACCOUNTTYPE,
                CASE
                    WHEN DA.USER_ID IS NOT NULL THEN DA.ATTR8_OLD
                    ELSE P.accountId
                END AS ACCOUNTID,
                P.Nombre AS NOMBRE,
                P.Apellido AS APELLIDO,
                P.NNombre AS NNOMBRE,
                P.NApellido AS NAPELLIDO,
                CASE
                    WHEN DA.USER_ID IS NOT NULL AND STRPOS(P.perfilA, ' ') > 0
                    THEN UPPER(DA.ISSUER_CODE || ' ' || SUBSTR(P.perfilA, STRPOS(P.perfilA, ' ') + 1))
                    ELSE UPPER(P.perfilA)
                END AS PERFILA,
                P.perfilB AS PERFILB,
                P.IdiomaA AS IDIOMAA,
                P.IdiomaB AS IDIOMAB,
                P.TelcoA AS TELCOA,
                P.TelcoB AS TELCOB,
                P.Razon AS RAZON,
                CASE
                    WHEN DA.USER_ID IS NOT NULL THEN UPPER(DA.ISSUER_CODE || ' ' || DA.GRADE_NAME_OLD)
                    ELSE P.PerfilCuenta
                END AS PERFILCUENTA,
                CASE
                    WHEN DA.USER_ID IS NOT NULL THEN UPPER(DA.ISSUER_CODE || ' ' || DA.GRADE_NAME_OLD)
                    ELSE P.PerfilCuentaA
                END AS PERFILCUENTAA,
                P.perfilCuentaB AS PERFILCUENTAB,
                P.TipoDocumentoA AS TIPODOCUMENTOA,
                P.TipoDocumentoB AS TIPODOCUMENTOB,
                P.DocumentoB AS DOCUMENTOB,
                P.NumDocumentoB AS NUMDOCUMENTOB,
                P.requestType AS REQUESTTYPE,
                P.oldData AS OLDDATA,
                P.newData AS NEWDATA,
                DA2.ATTR7_OLD AS USERIDOLD,
                DA2.ATTR8_OLD AS ACCOUNTIDOLD
            FROM PROCESS P
            LEFT JOIN WALLET_OLD DA ON P.O_USER_ID = DA.USER_ID 
                AND STRFTIME('%Y-%m-%d %H:%M', CAST(P.createdOn AS TIMESTAMP)) < STRFTIME('%Y-%m-%d %H:%M', CAST(DA.CREATED_AT AS TIMESTAMP))
                AND DA.ORDEN = 1
            LEFT JOIN WALLET_OLD DA2 ON P.O_USER_ID = DA2.USER_ID
                AND DA2.ORDEN = 1
            """

            # Guardar resultado final
            output_path = f"output/{date_folder}/LOG_USR.parquet"
            Path(f"output/{date_folder}").mkdir(parents=True, exist_ok=True)

            copy_query = f"""
            COPY ({query}) TO '{output_path}' (FORMAT PARQUET);
            """
            self.conn.execute(copy_query)

            # Verificar registros
            count_result = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{output_path}')").fetchone()
            record_count = count_result[0] if count_result else 0

            self.logger.info(f"SP_LOG_USR EXACTO completado: {record_count} registros -> {output_path}")

            # Verificar registros CHANGE_AUTH_FACTOR procesados
            try:
                change_auth_count = self.conn.execute(f"""
                    SELECT COUNT(*) FROM read_parquet('{output_path}')
                    WHERE REQUESTTYPE = 'CHANGE_AUTH_FACTOR'
                """).fetchone()[0]
                self.logger.info(f"  - CHANGE_AUTH_FACTOR: {change_auth_count:,} registros")
            except Exception as e:
                self.logger.warning(f"No se pudo verificar CHANGE_AUTH_FACTOR: {e}")

            # Limpiar tabla temporal
            try:
                self.conn.execute("DROP TABLE IF EXISTS wallet_old_temp")
            except:
                pass
            
            return output_path

        except Exception as e:
            self.logger.error(f"Error en SP_LOG_USR EXACTO: {e}")
            raise

    def apply_profile_conversions(self, fecha: str, date_folder: str) -> str:
        """
        Aplica las conversiones de perfiles usando conv_perfil.csv
        Replica exactamente la lógica del flujo original de procesar.py
        """
        try:
            self.logger.info(f"Aplicando conversiones de perfiles para fecha: {fecha}")

            log_usr_path = f"output/{date_folder}/LOG_USR.parquet"
            enriched_path = f"output/{date_folder}/LOG_USR_ENRICHED.parquet"

            # Cargar tabla de conversión
            if not self.load_conv_perfil_table():
                self.logger.warning("No se pudo cargar conv_perfil.csv, continuando sin conversiones")
                return log_usr_path

            # Query para enriquecer LOG_USR con conversiones de perfiles
            enrich_query = f"""
            CREATE OR REPLACE TABLE log_usr_enriched AS
            SELECT
                l.*,
                -- Extraer perfiles del JSON en OLDDATA (igual que procesar.py)
                CASE
                    WHEN l.oldData IS NOT NULL AND l.oldData != '' THEN
                        regexp_extract(l.oldData, '"authorizationProfileId":"([^"]+)"', 1)
                    ELSE NULL
                END AS authz_profile_old,

                CASE
                    WHEN l.oldData IS NOT NULL AND l.oldData != '' THEN
                        regexp_extract(l.oldData, '"marketingProfileId":"([^"]+)"', 1)
                    ELSE NULL
                END AS mkt_profile_old,

                CASE
                    WHEN l.oldData IS NOT NULL AND l.oldData != '' THEN
                        regexp_extract(l.oldData, '"securityProfileId":"([^"]+)"', 1)
                    ELSE NULL
                END AS sec_profile_old,

                -- Aplicar conversiones EXACTAS como procesar.py
                CASE
                    WHEN l.requestType = 'User Modification'
                         AND l.oldData IS NOT NULL
                         AND l.oldData != ''
                         AND regexp_extract(l.oldData, '"authorizationProfileId":"([^"]+)"', 1) IS NOT NULL
                         AND regexp_extract(l.oldData, '"marketingProfileId":"([^"]+)"', 1) IS NOT NULL
                         AND regexp_extract(l.oldData, '"securityProfileId":"([^"]+)"', 1) IS NOT NULL
                    THEN (
                        SELECT cp.CATEGORY_NAME
                        FROM conv_perfil_table cp
                        WHERE cp.AUTHZ_PRO_CODE = regexp_extract(l.oldData, '"authorizationProfileId":"([^"]+)"', 1)
                          AND cp.MKT_PRO_CODE = regexp_extract(l.oldData, '"marketingProfileId":"([^"]+)"', 1)
                          AND cp.SEC_PRO_CODE = regexp_extract(l.oldData, '"securityProfileId":"([^"]+)"', 1)
                        LIMIT 1
                    )
                    ELSE CAST(l.perfilB AS VARCHAR)
                END AS perfilB_converted,

                CASE
                    WHEN l.requestType = 'User Modification'
                         AND l.oldData IS NOT NULL
                         AND l.oldData != ''
                         AND regexp_extract(l.oldData, '"marketingProfileId":"([^"]+)"', 1) IS NOT NULL
                    THEN (
                        SELECT cp.MARKETING_PROFILE_NAME
                        FROM conv_perfil_table cp
                        WHERE cp.MKT_PRO_CODE = regexp_extract(l.oldData, '"marketingProfileId":"([^"]+)"', 1)
                        LIMIT 1
                    )
                    ELSE CAST(l.perfilCuentaB AS VARCHAR)
                END AS perfilCuentaB_converted

            FROM read_parquet('{log_usr_path}') l
            WHERE CAST(l.createdOn AS DATE) = CAST('{fecha}' AS DATE)
            """

            self.conn.execute(enrich_query)

            # Guardar versión enriquecida
            save_enriched_query = f"""
            COPY log_usr_enriched TO '{enriched_path}' (FORMAT PARQUET);
            """
            self.conn.execute(save_enriched_query)

            # Verificar registros enriquecidos
            count_result = self.conn.execute("SELECT COUNT(*) FROM log_usr_enriched").fetchone()
            record_count = count_result[0] if count_result else 0

            self.logger.info(f"Conversiones de perfiles aplicadas: {record_count} registros -> {enriched_path}")

            return enriched_path

        except Exception as e:
            self.logger.error(f"Error aplicando conversiones de perfiles: {e}")
            # Si falla, retornar el archivo original
            return f"output/{date_folder}/LOG_USR.parquet"

    def apply_oracle_exact_logic(self, log_usr_path: str, date_folder: str) -> str:
        """
        Aplica la lógica EXACTA de Oracle basada en análisis reverso del código original
        Oracle NO deduplica en SP_LOG_USR, sino que aplica lógica de selección posterior
        """
        try:
            self.logger.info("🔍 ANÁLISIS REVERSO: Aplicando lógica Oracle exacta")

            # Crear archivo con lógica Oracle
            oracle_path = f"output/{date_folder}/LOG_USR_ORACLE_LOGIC.parquet"

            # ANÁLISIS REVERSO: Oracle inserta TODOS los registros sin deduplicar
            # Luego aplica una lógica de selección específica no documentada en SP_LOG_USR

            # Basado en los 58 casos problemáticos, Oracle parece usar:
            # 1. Preferencia por eventos en horario específico
            # 2. Lógica de prioridad por contexto
            # 3. Filtros temporales no documentados

            # ULTRA INSTINTO PRO: Oracle incluye MÚLTIPLES eventos por documento
            # cuando tienen diferentes USERHISTID - NO deduplica por DOCUMENTO
            oracle_logic_query = f"""
            CREATE OR REPLACE TABLE log_usr_oracle_logic AS
            WITH oracle_selection AS (
                SELECT *,
                    -- CORRECCIÓN ULTRA INSTINTO PRO: Oracle deduplica SOLO por USERHISTID + REQUESTTYPE
                    -- NO por DOCUMENTO + REQUESTTYPE como pensábamos antes
                    ROW_NUMBER() OVER (
                        PARTITION BY USERHISTID, REQUESTTYPE
                        ORDER BY
                            -- CORRECCIÓN: Oracle toma el evento MÁS RECIENTE por USERHISTID + REQUESTTYPE
                            -- Documento ******** confirma: Oracle selecciona 09:41:55 (no 00:52:48)
                            CREATEDON DESC
                    ) as oracle_rank
                FROM read_parquet('{log_usr_path}')
            )
            SELECT
                USERHISTID, CREATEDON, TIPODOCUMENTO, DOCUMENTO, MSISDN, MSISDNB,
                BANKDOMAIN, CREATED_BY, USERID, ACCOUNTTYPE, ACCOUNTID, NOMBRE,
                APELLIDO, NNOMBRE, NAPELLIDO, PERFILA, PERFILB, IDIOMAA, IDIOMAB,
                TELCOA, TELCOB, RAZON, PERFILCUENTA, PERFILCUENTAA, PERFILCUENTAB,
                TIPODOCUMENTOA, TIPODOCUMENTOB, DOCUMENTOB, NUMDOCUMENTOB,
                REQUESTTYPE, OLDDATA, NEWDATA, USERIDOLD, ACCOUNTIDOLD
            FROM oracle_selection
            WHERE oracle_rank = 1
            ORDER BY CREATEDON
            """

            self.conn.execute(oracle_logic_query)

            # Guardar resultado
            self.conn.execute(f"COPY log_usr_oracle_logic TO '{oracle_path}' (FORMAT PARQUET);")

            # Verificar resultados
            count_original = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{log_usr_path}')").fetchone()[0]
            count_oracle = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{oracle_path}')").fetchone()[0]

            self.logger.info(f"✅ Lógica Oracle aplicada:")
            self.logger.info(f"   • Registros originales: {count_original:,}")
            self.logger.info(f"   • Registros con lógica Oracle: {count_oracle:,}")
            self.logger.info(f"   • Reducción: {count_original - count_oracle:,} registros")

            # Verificar casos específicos problemáticos
            self.verify_oracle_logic_cases(oracle_path)

            return oracle_path

        except Exception as e:
            self.logger.error(f"Error aplicando lógica Oracle: {e}")
            # Si falla, retornar el archivo original
            return log_usr_path

    def verify_oracle_logic_cases(self, oracle_path: str):
        """Verifica casos específicos con la nueva lógica Oracle"""
        try:
            # Casos problemáticos conocidos
            test_cases = ['********', '70635331', '77093123', '74965338']

            self.logger.info("🔍 Verificando casos problemáticos con lógica Oracle:")

            for documento in test_cases:
                result = self.conn.execute(f"""
                    SELECT REQUESTTYPE, CREATEDON
                    FROM read_parquet('{oracle_path}')
                    WHERE DOCUMENTO = '{documento}' AND REQUESTTYPE = 'CHANGE_AUTH_FACTOR'
                """).fetchall()

                if result:
                    for requesttype, createdon in result:
                        self.logger.info(f"   📄 Doc {documento}: {requesttype} - {createdon}")
                else:
                    self.logger.info(f"   📄 Doc {documento}: No encontrado")

        except Exception as e:
            self.logger.warning(f"Error verificando casos: {e}")

    def apply_profile_conversions_to_file(self, fecha: str, date_folder: str, source_file: str) -> str:
        """
        Aplica conversiones de perfiles a un archivo específico (como el deduplicado)
        """
        try:
            self.logger.info(f"Aplicando conversiones de perfiles al archivo: {source_file}")

            # Cargar tabla de conversión si no está cargada
            if not hasattr(self, '_conv_perfil_loaded'):
                self.load_conv_perfil_table()
                self._conv_perfil_loaded = True

            # Crear archivo enriquecido
            enriched_path = f"output/{date_folder}/LOG_USR_ENRICHED_FROM_DEDUPLICATED.parquet"

            # Query para aplicar conversiones al archivo específico
            conversion_query = f"""
            CREATE OR REPLACE TABLE log_usr_enriched AS
            SELECT
                l.*,
                COALESCE(cp_a.PERFIL_NOMBRE, l.perfilA) as perfilA_convertido,
                COALESCE(cp_b.PERFIL_NOMBRE, l.perfilB) as perfilB_convertido
            FROM read_parquet('{source_file}') l
            LEFT JOIN conv_perfil_table cp_a ON l.perfilA = cp_a.PERFIL_CODIGO
            LEFT JOIN conv_perfil_table cp_b ON l.perfilB = cp_b.PERFIL_CODIGO
            """

            self.conn.execute(conversion_query)

            # Exportar tabla enriquecida
            export_query = f"""
            COPY log_usr_enriched TO '{enriched_path}' (FORMAT PARQUET)
            """

            self.conn.execute(export_query)

            # Verificar resultado
            count = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{enriched_path}')").fetchone()[0]

            self.logger.info(f"Conversiones aplicadas al archivo: {count:,} registros -> {enriched_path}")

            return enriched_path

        except Exception as e:
            self.logger.error(f"Error aplicando conversiones al archivo: {e}")
            # Si falla, retornar archivo original
            return source_file

    def apply_original_deduplication_only(self, log_usr_path: str, date_folder: str) -> str:
        """
        Aplica SOLO la deduplicación exacta del flujo original SIN procesamiento JSON
        Oracle mantiene los REQUESTTYPE originales, NO los convierte a tipos específicos
        """
        try:
            self.logger.info(f"Aplicando SOLO deduplicación como Oracle original (SIN conversión de tipos)")

            # Crear archivo deduplicado manteniendo REQUESTTYPE originales
            deduplicated_path = f"output/{date_folder}/LOG_USR_DEDUPLICATED.parquet"

            # Query de deduplicación EXACTA como Oracle (USERHISTID + REQUESTTYPE únicos)
            # Oracle mantiene 17,305 combinaciones únicas de USERHISTID+REQUESTTYPE
            # CORRECCIÓN CRÍTICA: Documento ******** confirma que Oracle toma el MÁS RECIENTE
            # Oracle selecciona 09:41:55 (no 00:52:48) para CHANGE_AUTH_FACTOR duplicados
            dedup_query = f"""
            CREATE OR REPLACE TABLE log_usr_deduplicated AS
            SELECT DISTINCT ON (USERHISTID, REQUESTTYPE) *
            FROM read_parquet('{log_usr_path}')
            ORDER BY USERHISTID, REQUESTTYPE, CREATEDON DESC
            """

            self.conn.execute(dedup_query)

            # Exportar tabla deduplicada a parquet
            export_query = f"""
            COPY log_usr_deduplicated TO '{deduplicated_path}' (FORMAT PARQUET)
            """

            self.conn.execute(export_query)

            # Verificar resultados
            original_count = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{log_usr_path}')").fetchone()[0]
            deduplicated_count = self.conn.execute(f"SELECT COUNT(*) FROM read_parquet('{deduplicated_path}')").fetchone()[0]

            removed_duplicates = original_count - deduplicated_count

            self.logger.info(f"Deduplicación completada (manteniendo REQUESTTYPE originales):")
            self.logger.info(f"  - Registros originales: {original_count:,}")
            self.logger.info(f"  - Registros después de deduplicación: {deduplicated_count:,}")
            self.logger.info(f"  - Duplicados eliminados: {removed_duplicates:,}")

            # Verificar tipos de transacción mantenidos
            tipos_result = self.conn.execute(f"""
                SELECT REQUESTTYPE, COUNT(*) as count
                FROM read_parquet('{deduplicated_path}')
                GROUP BY REQUESTTYPE
                ORDER BY COUNT(*) DESC
            """).fetchall()

            self.logger.info(f"Tipos de transacción mantenidos:")
            for tipo, count in tipos_result[:10]:  # Top 10
                self.logger.info(f"  - {tipo}: {count:,} registros")

            return deduplicated_path

        except Exception as e:
            self.logger.error(f"Error en deduplicación: {e}")
            # Si falla, retornar archivo original
            return log_usr_path

    def export_to_csv(self, fecha: str, date_folder: str) -> list:
        """
        Exporta LOG_USR.parquet a CSV con conversiones de perfiles
        Integra la funcionalidad del flujo original
        """
        try:
            self.logger.info(f"Iniciando exportación CSV con conversiones de perfiles para fecha: {fecha}")

            log_usr_path = f"output/{date_folder}/LOG_USR.parquet"

            if not Path(log_usr_path).exists():
                raise FileNotFoundError(f"LOG_USR.parquet no encontrado: {log_usr_path}")

            # ANÁLISIS REVERSO ORACLE: SP_LOG_USR NO deduplica USER_AUTH_CHANGE_HISTORY
            # Oracle inserta TODOS los registros y aplica lógica de selección posterior
            self.logger.info("🔍 APLICANDO LÓGICA ORACLE EXACTA (sin deduplicación previa)")

            # PASO 1: Aplicar lógica Oracle post-procesamiento (como Oracle real)
            oracle_processed_path = self.apply_oracle_exact_logic(log_usr_path, date_folder)

            # PASO 2: Aplicar conversiones de perfiles AL ARCHIVO PROCESADO
            enriched_path = self.apply_profile_conversions_to_file(fecha, date_folder, oracle_processed_path)

            # Crear directorio de exportación CSV
            csv_export_path = f"output/{date_folder}/csv_exports"
            Path(csv_export_path).mkdir(parents=True, exist_ok=True)

            # OPTIMIZACIÓN: NO generar CSV principal redundante (ya tenemos LOG_USR.parquet)
            # Solo generar archivos segmentados por banco que SÍ son necesarios
            self.logger.info(f"Omitiendo CSV principal redundante - usando solo archivos Parquet y CSVs segmentados")

            # PASO 3: Exportaciones segmentadas (ÚNICOS archivos CSV necesarios)
            exported_files = []

            # Exportación por BankDomain (funcionalidad original) usando archivo ORIGINAL como Oracle
            exported_files.extend(self.export_by_bank_domain(fecha, date_folder, log_usr_path))

            # COMENTADO: Funcionalidad by_profile para mantener exactitud con flujo original
            # El flujo original solo genera archivos by_bank, no by_profile
            # if "ENRICHED" in enriched_path:
            #     profile_files = self.export_by_profile_conversions(fecha, date_folder, enriched_path)
            #     exported_files.extend(profile_files)
            #     self.logger.info(f"Archivos adicionales por perfil: {len(profile_files)}")

            self.logger.info(f"Estructura EXACTA como flujo original: solo by_bank (sin by_profile)")

            # PASO ADICIONAL: Aplicar procesamiento exacto como procesar.py original
            self.logger.info("🔄 APLICANDO PROCESAMIENTO EXACTO como procesar.py...")
            try:
                from procesar_log_usuarios import ProcesadorLogUsuarios

                procesador = ProcesadorLogUsuarios(self.logger)
                csv_final_dir = f"output/{date_folder}/csv_exports/csv_final_procesado"

                # CORRECCIÓN CRÍTICA: Usar archivo procesado con lógica Oracle exacta
                # para mantener la homologación correcta con Oracle
                archivos_procesados = procesador.procesar_log_usuarios(
                    oracle_processed_path, fecha, csv_final_dir
                )

                self.logger.info(f"✅ Procesamiento exacto completado: {len(archivos_procesados)} archivos")
                for archivo in archivos_procesados:
                    self.logger.info(f"  📁 {Path(archivo).name}")

                # Agregar archivos procesados a la lista de exportados
                exported_files.extend(archivos_procesados)

            except Exception as e:
                self.logger.error(f"❌ Error en procesamiento exacto: {e}")
                self.logger.info("⚠️ Continuando con archivos estándar...")

            # Limpiar archivos temporales
            self.cleanup_temp_files_export(date_folder)

            return exported_files

        except Exception as e:
            # Limpiar archivos temporales en caso de error
            self.cleanup_temp_files_export(date_folder)
            self.logger.error(f"Error en exportación CSV: {e}")
            raise

    def cleanup_temp_files_export(self, date_folder: str):
        """Limpia archivos temporales específicos de la exportación"""
        temp_files = [
            f"TEMP_LOGS_USUARIOS/{date_folder}/WALLET_OLD.parquet"
        ]

        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self.logger.info(f"Archivo temporal eliminado: {temp_file}")
            except Exception as e:
                self.logger.warning(f"No se pudo eliminar {temp_file}: {e}")

    def export_by_bank_domain(self, fecha: str, date_folder: str, log_usr_path: str) -> list:
        """
        Exporta LOG_USR segmentado por BankDomain
        Equivalente a log_usuarios/procesar.py
        """
        try:
            self.logger.info(f"Iniciando exportación segmentada por BankDomain")

            # CAMBIO CRÍTICO: Usar TODOS los bancos del sistema (como flujo original)
            # No solo los que tienen datos para esta fecha
            self.logger.info(f"Procesando TODOS los bancos del sistema: {self.bancos_sistema}")

            # Crear directorio de exportación segmentada
            segmented_export_path = f"output/{date_folder}/csv_exports/by_bank"
            Path(segmented_export_path).mkdir(parents=True, exist_ok=True)

            exported_files = []

            # Exportar por cada banco del sistema (EXACTO como flujo original)
            for domain in self.bancos_sistema:
                try:
                    fecha_formatted = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')

                    # Manejar registros NULL (sin BANKDOMAIN)
                    if domain is None:
                        domain_filename = f"LOG-USUARIOS-NULL-{fecha_formatted}.csv"
                        domain_condition = "BankDomain IS NULL"
                        domain_name = "NULL"
                    else:
                        domain_filename = f"LOG-USUARIOS-{domain}-{fecha_formatted}.csv"
                        domain_condition = f"BankDomain = '{domain}'"
                        domain_name = domain

                    domain_file_path = f"{segmented_export_path}/{domain_filename}"

                    # Query para exportar por dominio (incluyendo NULL)
                    domain_export_query = f"""
                    COPY (
                        SELECT *
                        FROM read_parquet('{log_usr_path}')
                        WHERE {domain_condition}
                        ORDER BY CREATEDON
                    ) TO '{domain_file_path}' (DELIMITER ',');
                    """

                    self.conn.execute(domain_export_query)

                    # Verificar registros
                    count_result = self.conn.execute(f"""
                        SELECT COUNT(*)
                        FROM read_parquet('{log_usr_path}')
                        WHERE {domain_condition}
                    """).fetchone()
                    record_count = count_result[0] if count_result else 0

                    if record_count > 0:
                        # Banco con datos - exportar normalmente
                        display_name = "NULL" if domain is None else domain
                        self.logger.info(f"Exportado dominio {display_name}: {record_count} registros -> {domain_file_path}")
                    else:
                        # EXACTO como flujo original: Crear archivo completamente vacío (SIN headers)
                        # El flujo original guarda archivos vacíos sin headers (línea 592: header=False)
                        with open(domain_file_path, 'w', newline='', encoding='utf-8') as csvfile:
                            pass  # Archivo completamente vacío

                        display_name = "NULL" if domain is None else domain
                        self.logger.info(f"Exportado dominio {display_name}: 0 registros (archivo vacío SIN headers) -> {domain_file_path}")

                    # Agregar TODOS los archivos (con datos y vacíos) como flujo original
                    exported_files.append(domain_file_path)

                except Exception as e:
                    self.logger.error(f"Error exportando dominio {domain}: {e}")
                    continue

            self.logger.info(f"Exportación segmentada completada: {len(exported_files)} archivos generados")
            return exported_files

        except Exception as e:
            self.logger.error(f"Error en exportación segmentada: {e}")
            raise

    def export_by_profile_conversions(self, fecha: str, date_folder: str, enriched_path: str) -> list:
        """
        Exporta archivos CSV segmentados por perfiles convertidos
        Replica la funcionalidad del flujo original que genera LOGUSR-{PERFIL}-{timestamp}.csv
        """
        try:
            self.logger.info(f"Iniciando exportación por perfiles convertidos")

            # Mapeo de perfiles a códigos de archivo (igual que el flujo original)
            perfil_mapping = {
                'FCOMPARTAMOS USUARIO FINAL': 'FCOMPARTAMOS',
                'FCOMPARTAMOS BIMER': 'FCOMPARTAMOS',
                'FCOMPARTAMOS AGENTE': 'FCOMPARTAMOS',
                'FCOMPARTAMOS AGENCIA': 'FCOMPARTAMOS',
                'FCOMPARTAMOS COMERCIO': 'FCOMPARTAMOS',
                'FCOMPARTAMOS SUPER AGENTE': 'FCOMPARTAMOS',
                'FCOMPARTAMOS SERVICE PROVIDER': 'FCOMPARTAMOS',
                'FCOMPARTAMOS AGENTE VIRTUAL': 'FCOMPARTAMOS',
                'FCOMPARTAMOS Remesas WU': 'FCOMPARTAMOS',
                'FCOMPARTAMOS Dispersor': 'FCOMPARTAMOS',
                'BNACION USUARIO FINAL': 'BNACION',
                'BNACION BIMER': 'BNACION',
                'BNACION AGENTE': 'BNACION',
                'BNACION AGENCIA': 'BNACION',
                'BNACION COMERCIO': 'BNACION',
                'BNACION SUPER AGENTE': 'BNACION',
                'BNACION SERVICE PROVIDER': 'BNACION',
                'BNACION AGENTE VIRTUAL': 'BNACION',
                'CRANDES USUARIO FINAL': 'CRANDES',
                'CRANDES BIMER': 'CRANDES',
                'CRANDES AGENTE': 'CRANDES',
                'CRANDES AGENCIA': 'CRANDES',
                'CRANDES COMERCIO': 'CRANDES',
                'CRANDES SUPER AGENTE': 'CRANDES',
                'CRANDES SERVICE PROVIDER': 'CRANDES',
                'INTERBANK USUARIO FINAL': 'INTERBANK',
                'INTERBANK BIMER': 'INTERBANK',
                'INTERBANK AGENTE': 'INTERBANK',
                'INTERBANK AGENCIA': 'INTERBANK',
                'INTERBANK COMERCIO': 'INTERBANK',
                'INTERBANK SUPER AGENTE': 'INTERBANK',
                'INTERBANK SERVICE PROVIDER': 'INTERBANK',
                'BCP USUARIO FINAL': 'BCP',
                'BCP BIMER': 'BCP',
                'BCP AGENTE': 'BCP',
                'BCP COMERCIO': 'BCP',
                'BCP SERVICE PROVIDER': 'BCP',
                'CCUSCO USUARIO FINAL': 'CCUSCO',
                'CCUSCO BIMER': 'CCUSCO',
                'CCUSCO AGENTE': 'CCUSCO',
                'CCUSCO AGENCIA': 'CCUSCO',
                'CCUSCO COMERCIO': 'CCUSCO',
                'CSULLANA USUARIO FINAL': 'CSULLANA',
                'CSULLANA BIMER': 'CSULLANA',
                'CSULLANA AGENTE': 'CSULLANA',
                'CSULLANA AGENCIA': 'CSULLANA',
                'CSF USUARIO FINAL': 'CSF',
                'CSF BIMER': 'CSF',
                'CSF AGENTE': 'CSF',
                'CSF AGENCIA': 'CSF',
                'CSF COMERCIO': 'CSF',
                'CSF AGENTE VIRTUAL': 'CSF',
                'CSF SERVICE PROVIDER': 'CSF',
                'CREDINKA USUARIO FINAL': 'CREDINKA',
                'CREDINKA BIMER': 'CREDINKA',
                'CREDINKA AGENTE': 'CREDINKA',
                'CREDINKA AGENCIA': 'CREDINKA',
                'CREDINKA COMERCIO': 'CREDINKA',
                'CREDINKA SERVICE PROVIDER': 'CREDINKA',
                '0231FCONFIANZA USUARIO FINAL': 'FCONFIANZA',
                '0231FCONFIANZA BIMER': 'FCONFIANZA',
                '0231FCONFIANZA AGENTE': 'FCONFIANZA',
                '0231FCONFIANZA AGENCIA': 'FCONFIANZA',
                '0231FCONFIANZA COMERCIO': 'FCONFIANZA',
                '0231FCONFIANZA SERVICE PROVIDER': 'FCONFIANZA',
                'FQAPAQ USUARIO FINAL': 'QAPAQ',
                'FQAPAQ BIMER': 'QAPAQ',
                'FQAPAQ AGENTE': 'QAPAQ',
                'FQAPAQ AGENCIA': 'QAPAQ',
                'FQAPAQ COMERCIO': 'QAPAQ',
                'GMONEY USUARIO FINAL': 'GMONEY',
                'GMONEY BIMER': 'GMONEY',
                'GMONEY AGENTE': 'GMONEY',
                'GMONEY AGENCIA': 'GMONEY',
                'GMONEY COMERCIO': 'GMONEY',
                'GMONEY AGENTE VIRTUAL': 'GMONEY',
                'GMONEY SERVICE PROVIDER': 'GMONEY',
                'CMACICA USUARIO FINAL': 'CMACICA',
                'CMACICA AGENTE VIRTUAL': 'CMACICA',
                'FOH BIMER': 'FOH',
                'FOH USUARIO FINAL': 'FOH',
                'BALFIN USUARIO FINAL': 'BALFIN',
                'BCOMVIVA USUARIO FINAL': 'BCOMVIVA'
            }

            # Crear directorio de exportación por perfiles
            profile_export_path = f"output/{date_folder}/csv_exports/by_profile"
            Path(profile_export_path).mkdir(parents=True, exist_ok=True)

            exported_files = []

            # Obtener perfiles únicos que tienen conversiones
            profiles_result = self.conn.execute(f"""
                SELECT DISTINCT perfilB_converted, COUNT(*) as total
                FROM log_usr_enriched
                WHERE perfilB_converted IS NOT NULL
                  AND perfilB_converted != ''
                  AND CAST(createdOn AS DATE) = CAST('{fecha}' AS DATE)
                GROUP BY perfilB_converted
                ORDER BY perfilB_converted
            """).fetchall()

            self.logger.info(f"Encontrados {len(profiles_result)} perfiles únicos para exportación")

            # Generar archivo para cada perfil
            for perfil_nombre, record_count in profiles_result:
                try:
                    # Buscar código de archivo correspondiente
                    archivo_codigo = perfil_mapping.get(perfil_nombre, perfil_nombre.replace(' ', '_'))

                    if record_count == 0:
                        continue

                    # Generar nombre de archivo con timestamp (igual que el flujo original)
                    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                    filename = f"LOGUSR-{archivo_codigo}-{timestamp}.csv"
                    full_path = f"{profile_export_path}/{filename}"

                    # Query de exportación (sin las columnas de conversión en el output)
                    export_query = f"""
                    COPY (
                        SELECT
                            userHistId, createdOn, TipoDocumento, Documento, Msisdn, MsisdnB,
                            BankDomain, created_by, userId, accountType, accountId, Nombre,
                            Apellido, NNombre, NApellido, perfilA, perfilB, IdiomaA, IdiomaB,
                            TelcoA, TelcoB, Razon, PerfilCuenta, PerfilCuentaA, perfilCuentaB,
                            TipoDocumentoA, TipoDocumentoB, DocumentoB, NumDocumentoB,
                            requestType, oldData, newData, userIdOld, accountIdOld
                        FROM log_usr_enriched
                        WHERE perfilB_converted = '{perfil_nombre}'
                          AND CAST(createdOn AS DATE) = CAST('{fecha}' AS DATE)
                        ORDER BY createdOn
                    ) TO '{full_path}' (HEADER, DELIMITER ',');
                    """

                    self.conn.execute(export_query)
                    exported_files.append(full_path)
                    self.logger.info(f"Exportado perfil {archivo_codigo}: {record_count:,} registros -> {filename}")

                except Exception as e:
                    self.logger.error(f"Error exportando perfil {perfil_nombre}: {e}")
                    continue

            self.logger.info(f"Exportación por perfiles completada: {len(exported_files)} archivos generados")
            return exported_files

        except Exception as e:
            self.logger.error(f"Error en exportación por perfiles: {e}")
            return []

    def cleanup_temp_files(self, date_folder: str):
        """Limpia archivos temporales"""
        try:
            import shutil
            temp_path = Path(f"TEMP_LOGS_USUARIOS/{date_folder}")
            if temp_path.exists():
                shutil.rmtree(temp_path)
                self.logger.info(f"Archivos temporales eliminados: {temp_path}")
        except Exception as e:
            self.logger.warning(f"Error limpiando archivos temporales: {e}")

    def run_pipeline(self, fecha_input: str = None, process_part: str = None):
        """
        Ejecuta el pipeline completo de LOG_USUARIOS

        Args:
            fecha_input: Fecha en formato YYYY/MM/DD (opcional, por defecto ayer)
            process_part: Parte específica a ejecutar (opcional)
        """
        try:
            # Procesar fecha
            if not fecha_input:
                fecha_input = (datetime.now() - timedelta(days=1)).strftime('%Y/%m/%d')

            # Convertir formato de fecha
            fecha = datetime.strptime(fecha_input, '%Y/%m/%d').strftime('%Y-%m-%d')
            date_folder = datetime.strptime(fecha, '%Y-%m-%d').strftime('%Y%m%d')

            self.logger.info(f"=== INICIANDO PIPELINE LOG_USUARIOS MODERNIZADO ===")
            self.logger.info(f"Fecha de procesamiento: {fecha} (carpeta: {date_folder})")

            # Paso 1: Extracción de datos
            if not process_part or process_part == "USER-MODIFY":
                self.log_execution_status("USER-MODIFY", "INICIANDO")
                self.extract_user_account_history(fecha, date_folder)
                self.log_execution_status("USER-MODIFY", "OK")

            # Paso 2: Procesamiento PRE-LOG-USR
            if not process_part or process_part == "PRE-LOG-USR":
                self.log_execution_status("PRE-LOG-USR", "INICIANDO")
                self.process_sp_pre_log_usr(fecha, date_folder)
                self.process_sp_user_modification(fecha, date_folder)
                self.process_sp_user_auth_day(fecha, date_folder)
                self.log_execution_status("PRE-LOG-USR", "OK")

            # Paso 3: Procesamiento LOG-USR
            if not process_part or process_part == "LOG-USR":
                self.log_execution_status("LOG-USR", "INICIANDO")
                self.process_sp_log_usr(fecha, date_folder)
                self.log_execution_status("LOG-USR", "OK")

            # Paso 4: Exportación CSV con conversiones de perfiles
            if not process_part or process_part == "EXPORT-CSV":
                self.log_execution_status("EXPORT-CSV", "INICIANDO")
                exported_files = self.export_to_csv(fecha, date_folder)
                self.log_execution_status("EXPORT-CSV", "OK")

                self.logger.info(f"=== ARCHIVOS CSV GENERADOS ===")
                self.logger.info(f"Total de archivos CSV segmentados: {len(exported_files)}")

                # Solo archivos segmentados por banco (optimizado - sin CSV principal redundante)
                bank_files = [f for f in exported_files if 'by_bank' in f]

                if bank_files:
                    self.logger.info(f"Archivos por BankDomain ({len(bank_files)}) - EXACTO como flujo original:")
                    for file_path in bank_files:
                        self.logger.info(f"  - {Path(file_path).name}")

                self.logger.info(f"Estructura OPTIMIZADA: solo archivos segmentados (datos completos en Parquet)")

            self.logger.info(f"=== PIPELINE COMPLETADO EXITOSAMENTE ===")

            # Mostrar resumen de archivos generados
            self.show_generated_files(date_folder)

            return True

        except Exception as e:
            self.logger.error(f"Error en pipeline: {e}")
            self.log_execution_status("PIPELINE", "FAILED")
            raise
        finally:
            self.conn.close()

    def show_generated_files(self, date_folder: str):
        """Muestra información de archivos generados"""
        self.logger.info(f"\n=== ARCHIVOS GENERADOS ===")

        # Archivos temporales
        temp_path = Path(f"TEMP_LOGS_USUARIOS/{date_folder}")
        if temp_path.exists():
            self.logger.info(f"Archivos temporales en {temp_path}:")
            for file in temp_path.glob("*.parquet"):
                size = file.stat().st_size / (1024*1024)  # MB
                self.logger.info(f"  - {file.name} ({size:.2f} MB)")

        # Archivos de salida
        output_path = Path(f"output/{date_folder}")
        if output_path.exists():
            self.logger.info(f"Archivos de salida en {output_path}:")
            for file in output_path.rglob("*"):
                if file.is_file():
                    size = file.stat().st_size / (1024*1024)  # MB
                    rel_path = file.relative_to(output_path)
                    self.logger.info(f"  - {rel_path} ({size:.2f} MB)")


def main():
    """Función principal"""
    import argparse

    parser = argparse.ArgumentParser(description='Pipeline ETL LOG_USUARIOS Modernizado con Conversiones de Perfiles')
    parser.add_argument('fecha', nargs='?', help='Fecha en formato YYYY/MM/DD (opcional, por defecto ayer)')
    parser.add_argument('proceso', nargs='?', help='Proceso específico: USER-MODIFY, PRE-LOG-USR, LOG-USR, EXPORT-CSV')
    parser.add_argument('--cleanup', action='store_true', help='Limpiar archivos temporales al finalizar')

    args = parser.parse_args()

    try:
        # Crear y ejecutar pipeline
        pipeline = LogUsuariosPipeline()
        pipeline.run_pipeline(args.fecha, args.proceso)

        # Limpiar archivos temporales si se solicita
        if args.cleanup and args.fecha:
            date_folder = datetime.strptime(args.fecha, '%Y/%m/%d').strftime('%Y%m%d')
            pipeline.cleanup_temp_files(date_folder)

        print("\n🎉 Pipeline ejecutado exitosamente!")

    except Exception as e:
        print(f"\n❌ Error en pipeline: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
