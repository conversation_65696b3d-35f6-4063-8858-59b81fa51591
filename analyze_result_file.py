#!/usr/bin/env python3
"""
Script para validar el archivo de resultado y verificar si las correcciones
de CNOMBRE están funcionando correctamente.
"""

import pandas as pd
import csv
import sys
from datetime import datetime

def analyze_result_file(filepath):
    """
    Analizar el archivo de resultado para verificar las correcciones CNOMBRE
    """
    print("=== ANÁLISIS DEL ARCHIVO DE RESULTADO ===")
    print(f"Archivo: {filepath}")
    print(f"Fecha de análisis: {datetime.now()}")
    print()
    
    try:
        # Leer el archivo CSV sin headers (como se genera)
        df = pd.read_csv(filepath, header=None)
        print(f"✅ Archivo cargado exitosamente")
        print(f"📊 Total de registros: {len(df):,}")
        print(f"📊 Total de columnas: {len(df.columns)}")
        print()
        
        # Definir nombres de columnas según la estructura esperada (30 columnas)
        column_names = [
            'TipoTransaccion',     # 0
            'TransactionID',       # 1
            'DiaHora',            # 2
            'TipoCuenta',         # 3
            'TipoDocumento',      # 4
            'Documento',          # 5
            'MSISDN',             # 6
            'BankDomain',         # 7
            'Nombres',            # 8
            'Apellidos',          # 9
            'PerfilA',            # 10
            'PerfilB',            # 11
            'IdiomaA',            # 12
            'IdiomaB',            # 13
            'TelcoA',             # 14
            'TelcoB',             # 15
            'Razon',              # 16
            'Initiating User',    # 17
            'MSISDNB',            # 18
            'NNombre',            # 19 - COLUMNA 20 (índice 19)
            'NApellido',          # 20 - COLUMNA 21 (índice 20)
            'ID USUARIO',         # 21
            'ID CUENTA',          # 22
            'PerfilCuenta',       # 23
            'PerfilCuentaA',      # 24
            'PerfilCuentaB',      # 25
            'TipoDocumentoA',     # 26
            'TipoDocumentoB',     # 27
            'NumDocumentoA',      # 28
            'NumDocumentoB'       # 29
        ]
        
        # Asignar nombres de columnas
        if len(df.columns) >= len(column_names):
            df.columns = column_names + [f'Extra_{i}' for i in range(len(column_names), len(df.columns))]
        else:
            df.columns = column_names[:len(df.columns)]
        
        # ANÁLISIS ESPECÍFICO DE CNOMBRE
        print("🔍 ANÁLISIS ESPECÍFICO DE REGISTROS CNOMBRE:")
        print("=" * 50)
        
        cnombre_records = df[df['TipoTransaccion'] == 'CNOMBRE']
        print(f"📊 Registros CNOMBRE encontrados: {len(cnombre_records):,}")
        
        if len(cnombre_records) == 0:
            print("❌ ERROR: No se encontraron registros CNOMBRE en el archivo")
            return False
        
        # Verificar columnas 20 y 21 (NNombre y NApellido)
        print("\n🔍 ANÁLISIS DE COLUMNAS 20 (NNombre) y 21 (NApellido):")
        print("-" * 55)
        
        # Columna 20 - NNombre (índice 19)
        if 'NNombre' in df.columns:
            nombres_vacios = cnombre_records['NNombre'].isna().sum() + (cnombre_records['NNombre'] == '').sum()
            nombres_con_datos = len(cnombre_records) - nombres_vacios
            
            print(f"COLUMNA 20 (NNombre):")
            print(f"  - Total registros CNOMBRE: {len(cnombre_records):,}")
            print(f"  - Nombres vacíos: {nombres_vacios:,}")
            print(f"  - Nombres con datos: {nombres_con_datos:,}")
            print(f"  - Porcentaje con datos: {(nombres_con_datos/len(cnombre_records)*100):.1f}%")
            
            if nombres_con_datos > 0:
                print(f"  ✅ HAY NOMBRES EN LA COLUMNA 20!")
                # Mostrar ejemplos
                ejemplos_nombres = cnombre_records[cnombre_records['NNombre'].notna() & (cnombre_records['NNombre'] != '')]['NNombre'].head(5)
                print(f"  📝 Ejemplos de nombres:")
                for i, nombre in enumerate(ejemplos_nombres, 1):
                    print(f"     {i}. '{nombre}'")
            else:
                print(f"  ❌ COLUMNA 20 ESTÁ VACÍA")
        
        # Columna 21 - NApellido (índice 20)
        if 'NApellido' in df.columns:
            apellidos_vacios = cnombre_records['NApellido'].isna().sum() + (cnombre_records['NApellido'] == '').sum()
            apellidos_con_datos = len(cnombre_records) - apellidos_vacios
            
            print(f"\nCOLUMNA 21 (NApellido):")
            print(f"  - Total registros CNOMBRE: {len(cnombre_records):,}")
            print(f"  - Apellidos vacíos: {apellidos_vacios:,}")
            print(f"  - Apellidos con datos: {apellidos_con_datos:,}")
            print(f"  - Porcentaje con datos: {(apellidos_con_datos/len(cnombre_records)*100):.1f}%")
            
            if apellidos_con_datos > 0:
                print(f"  ✅ HAY APELLIDOS EN LA COLUMNA 21!")
                # Mostrar ejemplos
                ejemplos_apellidos = cnombre_records[cnombre_records['NApellido'].notna() & (cnombre_records['NApellido'] != '')]['NApellido'].head(5)
                print(f"  📝 Ejemplos de apellidos:")
                for i, apellido in enumerate(ejemplos_apellidos, 1):
                    print(f"     {i}. '{apellido}'")
            else:
                print(f"  ❌ COLUMNA 21 ESTÁ VACÍA")
        
        # Análisis combinado
        print(f"\n🔍 ANÁLISIS COMBINADO:")
        print("-" * 25)
        
        if 'NNombre' in df.columns and 'NApellido' in df.columns:
            registros_completos = cnombre_records[
                (cnombre_records['NNombre'].notna()) & 
                (cnombre_records['NNombre'] != '') & 
                (cnombre_records['NApellido'].notna()) & 
                (cnombre_records['NApellido'] != '')
            ]
            
            print(f"Registros con NOMBRE Y APELLIDO completos: {len(registros_completos):,}")
            
            if len(registros_completos) > 0:
                print(f"✅ ÉXITO: Hay registros con nombres y apellidos completos")
                print(f"📝 Ejemplos de registros completos:")
                for i, (_, row) in enumerate(registros_completos.head(3).iterrows(), 1):
                    print(f"   {i}. Nombre: '{row['NNombre']}' | Apellido: '{row['NApellido']}'")
            else:
                print(f"❌ ERROR: No hay registros con nombres y apellidos completos")
        
        # Verificar otros tipos de transacción para contexto
        print(f"\n📊 RESUMEN POR TIPO DE TRANSACCIÓN:")
        print("-" * 40)
        tipo_counts = df['TipoTransaccion'].value_counts()
        for tipo, count in tipo_counts.head(10).items():
            print(f"  {tipo}: {count:,} registros")
        
        # EVALUACIÓN FINAL
        print(f"\n" + "="*60)
        print(f"EVALUACIÓN FINAL DE LAS CORRECCIONES:")
        print(f"="*60)
        
        if 'NNombre' in df.columns and 'NApellido' in df.columns:
            nombres_ok = (cnombre_records['NNombre'].notna() & (cnombre_records['NNombre'] != '')).sum()
            apellidos_ok = (cnombre_records['NApellido'].notna() & (cnombre_records['NApellido'] != '')).sum()
            
            if nombres_ok > 0 and apellidos_ok > 0:
                print(f"✅ CORRECCIONES EXITOSAS:")
                print(f"   - Columna 20 (NNombre): {nombres_ok:,} registros con datos")
                print(f"   - Columna 21 (NApellido): {apellidos_ok:,} registros con datos")
                print(f"   - Las correcciones de CNOMBRE están funcionando!")
                return True
            else:
                print(f"❌ CORRECCIONES FALLIDAS:")
                print(f"   - Columna 20 (NNombre): {nombres_ok:,} registros con datos")
                print(f"   - Columna 21 (NApellido): {apellidos_ok:,} registros con datos")
                print(f"   - Las columnas siguen vacías")
                return False
        else:
            print(f"❌ ERROR: No se encontraron las columnas NNombre o NApellido")
            return False
            
    except Exception as e:
        print(f"❌ Error analizando archivo: {e}")
        return False

def main():
    """
    Función principal
    """
    filepath = "/tmp/resultado_actual.csv"
    success = analyze_result_file(filepath)
    
    if success:
        print(f"\n🎉 RESULTADO: Las correcciones están funcionando correctamente!")
    else:
        print(f"\n❌ RESULTADO: Las correcciones no están funcionando como esperado")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
