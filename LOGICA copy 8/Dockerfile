# REPORTE_LOG_USUARIO - Proceso LOGICA
# Autor: Sistema de Reportes
# Última actualización: 2025-06-11

# Usar una imagen base de Python optimizada para producción
FROM python:3.9-slim

# Establecer variables de entorno
ENV PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=UTF-8 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    # Configuraciones específicas para gestión de memoria
    MALLOC_ARENA_MAX=2 \
    MALLOC_TRIM_THRESHOLD_=65536 \
    MALLOC_MMAP_THRESHOLD_=65536 \
    PYTHONMALLOC=malloc \
    # Configuración de DuckDB para reportes
    DUCKDB_MEMORY_LIMIT="6GB" \
    DUCKDB_THREADS=4 \
    # Configuración de pandas y numpy
    PANDAS_MAX_ROWS=100000 \
    PANDAS_CHUNKSIZE=10000 \
    NUMPY_MAX_THREADS=4

# Instalar herramientas necesarias y limpiar caché
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev \
    gcc \
    python3-dev \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

WORKDIR /app

# Copiar requirements.txt primero
COPY requirements.txt .

# Instalar dependencias optimizadas con mayor timeout y reintentos
RUN pip install --no-cache-dir --timeout 300 --retries 5 duckdb==0.9.2 && \
    pip install --no-cache-dir --timeout 300 --retries 5 boto3==1.34.0 && \
    pip install --no-cache-dir --timeout 300 --retries 5 pandas==2.0.3 && \
    pip install --no-cache-dir --timeout 300 --retries 5 fastparquet==2023.7.0 && \
    pip install --no-cache-dir --timeout 300 --retries 5 s3fs==0.4.2 && \
    pip install --no-cache-dir --timeout 300 --retries 5 -r requirements.txt

# Script de inicio para configurar límites de memoria
COPY <<EOF /app/entrypoint.sh
#!/bin/bash
# Configurar límites de memoria del sistema
ulimit -v 7340032  # 7GB en KB
ulimit -m 7340032  # 7GB en KB

# Configurar Python para usar menos memoria
export PYTHONMALLOC=malloc
export MALLOC_TRIM_THRESHOLD_=65536
export MALLOC_ARENA_MAX=2

# Configurar DuckDB para uso eficiente de memoria
export DUCKDB_MEMORY_LIMIT="6GB"
export DUCKDB_TEMPORARY_DIRECTORY="/tmp/duckdb-workspace"

# Crear directorio temporal para DuckDB
mkdir -p \$DUCKDB_TEMPORARY_DIRECTORY

# Ejecutar la aplicación con los argumentos pasados
cd /app
exec python3 -X faulthandler run_reporte.py "\$@"
EOF

RUN chmod +x /app/entrypoint.sh

# Copiar el código de la aplicación
COPY . .

# Crear directorio temporal para DuckDB
RUN mkdir -p /tmp/duckdb-workspace && \
    chmod 777 /tmp/duckdb-workspace

# Verificar instalaciones críticas
RUN python3 -c "import duckdb; print('✅ DuckDB instalado correctamente')" && \
    python3 -c "import boto3; print('✅ Boto3 instalado correctamente')" && \
    python3 -c "import fastparquet; print('✅ FastParquet instalado correctamente')" && \
    python3 -c "import s3fs; print('✅ S3FS instalado correctamente')" && \
    python3 -c "import pandas; print('✅ Pandas instalado correctamente')"

# Healthcheck para monitorear uso de memoria
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python3 -c 'import psutil; exit(0) if psutil.virtual_memory().percent < 85 else exit(1)'

# Comando para ejecutar el script
CMD ["python3", "run_reporte.py", "REPORTE_LOG_USUARIO"]
