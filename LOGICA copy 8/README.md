# Sistema Genérico de Reportes ETL

Este sistema permite ejecutar reportes ETL configurables que leen datos desde S3, los procesan mediante consultas SQL y los exportan a archivos Parquet.

## Estructura de carpetas

```
LOGICA/
  ├── CONFIGURACIONES/
  │   └── REPORTE_FULLCARGA/
  │       └── config.ini
  ├── REPORTES/
  │   └── REPORTE_FULLCARGA/
  │       └── queries/
  │           ├── fullcarga_cashin.sql
  │           ├── fullcarga_cashout.sql
  │           └── fullcarga_all.sql
  └── run_reporte.py
```

## Requisitos

- Python 3.6+
- Paquetes: duckdb, boto3
- Credenciales de AWS configuradas

## Instalación

```bash
pip install duckdb boto3
```

## Uso

Para ejecutar un reporte específico:

```bash
cd LOGICA
python run_reporte.py NOMBRE_REPORTE
```

Ejemplo:

```bash
python run_reporte.py REPORTE_FULLCARGA
```

## Configuración

Cada reporte tiene su propio archivo de configuración en la carpeta `CONFIGURACIONES/NOMBRE_REPORTE/config.ini`.

### Ejemplo de configuración (config.ini)

```ini
[general]
nombre_reporte = REPORTE_FULLCARGA
output_dir = REPORTE_CONCILIACION_FULLCARGAS
log_dir = logs

[fecha]
# Si rango = true, se usa rango fijo (rango_inicio y rango_fin)
# Si rango = false, se usa modo relativo (dias_atras)
rango = true
# Fechas para rango fijo
rango_inicio = 2025-05-06
rango_fin = 2025-05-07
# Días hacia atrás para modo relativo
dias_atras = 1

[s3_sources]
# Definir múltiples orígenes S3
# Formato: nombre_origen = bucket, prefix, region

# Origen principal
main = prd-datalake-bronze-zone-637423440311, DBMDWPROD/PRE_CONCILIACION_PROD, us-east-1

# Origen para verificar transacciones reversadas
cashin_prod = prd-datalake-bronze-zone-637423440311, DBMDWPROD/CASHIN_PROD, us-east-1

[queries]
# Lista de queries a ejecutar (archivos .sql en la carpeta queries)
query_list = fullcarga_all
```

## Consultas SQL

Las consultas SQL se almacenan en la carpeta `REPORTES/NOMBRE_REPORTE/queries/` y pueden usar parámetros que serán reemplazados al ejecutar:

### Parámetros disponibles

- `{fecha_inicio}`: Fecha de inicio del rango (YYYY-MM-DD)
- `{fecha_fin}`: Fecha de fin del rango (YYYY-MM-DD)
- `{year}`, `{month}`, `{day}`: Componentes de la fecha
- `{source_name_path}`: Ruta S3 completa para el origen definido (ej: `{main_path}`)
- `{source_name_bucket}`: Nombre del bucket para el origen definido (ej: `{main_bucket}`)
- `{source_name_prefix}`: Prefijo para el origen definido (ej: `{main_prefix}`)

## Salida

Los resultados se almacenan en archivos Parquet con la siguiente estructura:

```
OUTPUT_DIR/YYYY/MM/DD/NOMBRE_REPORTE_YYYY-MM-DD_HHMMSS.parquet
```

## Logs

Los logs se almacenan en la carpeta definida en la configuración (`log_dir`) con el formato:

```
logs/NOMBRE_REPORTE_YYYYMMDD_HHMMSS.log
```

## Cómo crear un nuevo reporte

1. Crear una carpeta para la configuración:
   ```
   mkdir -p CONFIGURACIONES/NUEVO_REPORTE
   ```

2. Crear el archivo de configuración:
   ```
   cp CONFIGURACIONES/REPORTE_FULLCARGA/config.ini CONFIGURACIONES/NUEVO_REPORTE/
   ```

3. Crear la estructura para las consultas:
   ```
   mkdir -p REPORTES/NUEVO_REPORTE/queries
   ```

4. Crear las consultas SQL necesarias en la carpeta `REPORTES/NUEVO_REPORTE/queries/`

5. Ejecutar el reporte:
   ```
   python run_reporte.py NUEVO_REPORTE
   ```
