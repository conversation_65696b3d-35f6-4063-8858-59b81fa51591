#!/usr/bin/env python3
"""
Script para debuggear exactamente dónde se pierden los valores firstName/lastName
y se convierten en 'None, None'
"""

import pandas as pd
import json
import os
import sys

def debug_none_values():
    """Debuggea dónde se convierten los valores en None"""
    
    # Leer datos parquet
    parquet_path = '/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER/output/20250610/LOG_USR.parquet'
    print(f"📂 Leyendo datos desde: {parquet_path}")
    
    if not os.path.exists(parquet_path):
        print(f"❌ Archivo no encontrado: {parquet_path}")
        return
    
    df = pd.read_parquet(parquet_path)
    print(f"📊 Total registros: {len(df):,}")
    
    # Buscar registros con cambios reales de firstName/lastName
    print(f"\n🔍 Buscando registros con cambios de firstName/lastName...")
    
    records_with_name_changes = []
    
    for idx, row in df.head(5000).iterrows():  # Revisar más registros
        if pd.notna(row['OLDDATA']) and row['OLDDATA'] and pd.notna(row['NEWDATA']) and row['NEWDATA']:
            try:
                old_data = json.loads(row['OLDDATA'])
                new_data = json.loads(row['NEWDATA'])
                
                # Buscar cambios específicos en firstName/lastName
                old_profile = old_data.get('profileDetails', {})
                new_profile = new_data.get('profileDetails', {})
                
                # Manejar si profileDetails es una lista
                if isinstance(old_profile, list) and old_profile:
                    old_profile = old_profile[0]
                if isinstance(new_profile, list) and new_profile:
                    new_profile = new_profile[0]
                
                # Si no son diccionarios, saltar
                if not isinstance(old_profile, dict) or not isinstance(new_profile, dict):
                    continue
                
                old_firstName = old_profile.get('firstName')
                old_lastName = old_profile.get('lastName')
                new_firstName = new_profile.get('firstName')
                new_lastName = new_profile.get('lastName')
                
                # Si hay cambios en nombres o si algún valor es None
                if (old_firstName != new_firstName or old_lastName != new_lastName or 
                    old_firstName is None or old_lastName is None or
                    new_firstName is None or new_lastName is None):
                    records_with_name_changes.append((idx, row, old_data, new_data))
                    
            except json.JSONDecodeError:
                continue
    
    print(f"📊 Registros con cambios de nombres encontrados: {len(records_with_name_changes)}")
    
    if not records_with_name_changes:
        print("❌ No se encontraron registros con cambios de nombres")
        # Buscar casos donde haya valores None
        print("\n🔍 Buscando registros con valores None en JSON...")
        
        for idx, row in df.head(1000).iterrows():
            if pd.notna(row['OLDDATA']) and row['OLDDATA']:
                try:
                    old_data = json.loads(row['OLDDATA'])
                    
                    # Buscar valores None recursivamente
                    def find_none_values(data, path=""):
                        results = []
                        if isinstance(data, dict):
                            for key, value in data.items():
                                if value is None:
                                    results.append(f"{path}.{key}: None")
                                else:
                                    results.extend(find_none_values(value, f"{path}.{key}"))
                        elif isinstance(data, list):
                            for i, item in enumerate(data):
                                results.extend(find_none_values(item, f"{path}[{i}]"))
                        return results
                    
                    none_values = find_none_values(old_data)
                    if none_values:
                        print(f"\n📝 Registro {row['USERHISTID']} tiene valores None:")
                        for none_val in none_values[:5]:  # Solo primeros 5
                            print(f"  {none_val}")
                        break
                        
                except json.JSONDecodeError:
                    continue
        return
    
    # Analizar algunos registros con cambios
    for i, (idx, row, old_data, new_data) in enumerate(records_with_name_changes[:3]):
        print(f"\n🔍 === ANÁLISIS REGISTRO CON CAMBIOS {i+1} ===")
        print(f"USERHISTID: {row['USERHISTID']}")
        print(f"NOMBRE: {row['NOMBRE']}")
        print(f"APELLIDO: {row['APELLIDO']}")
        
        # Simular la función compare_data_recursive
        new_data = {}
        if pd.notna(row['NEWDATA']) and row['NEWDATA']:
            try:
                new_data = json.loads(row['NEWDATA'])
            except:
                pass
        
        print(f"\n🧪 Simulando compare_data_recursive...")
        
        # Simular mapeo de campos
        field_mapping = {
            'firstName': 'CNOMBRE',
            'lastName': 'CNOMBRE',
            'preferredLanguage': 'CIDIOMA',
            'msisdn': 'CCEL',
            'mobileNumber': 'CCEL',
            'attr1': 'CTELCO'
        }
        
        def compare_data_recursive(old_data, new_data, parent_key=''):
            differences = []
            
            if isinstance(old_data, dict) and isinstance(new_data, dict):
                for key in old_data.keys() | new_data.keys():
                    new_key = f"{parent_key}.{key}" if parent_key else key
                    differences.extend(compare_data_recursive(old_data.get(key), new_data.get(key), new_key))
            elif isinstance(old_data, list) and isinstance(new_data, list):
                for idx_item, (old_item, new_item) in enumerate(zip(old_data, new_data)):
                    new_key = f"{parent_key}[{idx_item}]"
                    differences.extend(compare_data_recursive(old_item, new_item, new_key))
            elif old_data != new_data:
                differences.append({
                    "campo": parent_key,
                    "old_value": old_data,
                    "new_value": new_data
                })
            
            return differences
        
        differences = compare_data_recursive(old_data, new_data)
        
        print(f"Diferencias encontradas: {len(differences)}")
        
        changes = []
        for diff in differences:
            import re
            key = re.sub(r'\[\d*\]', '', diff['campo'])
            final_key = key.split('.')[-1]
            transaction_type = field_mapping.get(final_key, final_key)
            
            if transaction_type == 'CNOMBRE':
                print(f"  🎯 CNOMBRE encontrado:")
                print(f"    Campo: {diff['campo']}")
                print(f"    final_key: {final_key}")
                print(f"    old_value: {repr(diff['old_value'])}")
                print(f"    new_value: {repr(diff['new_value'])}")
                print(f"    old_value type: {type(diff['old_value'])}")
                print(f"    str(old_value): '{str(diff['old_value'])}'")
            
            changes.append({
                'field': transaction_type,
                'old_value': str(diff['old_value']),
                'new_value': str(diff['new_value'])
            })
        
        # Filtrar cambios CNOMBRE
        cnombre_changes = [c for c in changes if c['field'] == 'CNOMBRE']
        if cnombre_changes:
            print(f"\n📝 Cambios CNOMBRE generados:")
            for change in cnombre_changes:
                print(f"  old_value: '{change['old_value']}'")
                print(f"  new_value: '{change['new_value']}'")
        
        # Simular agrupación
        print(f"\n📊 Simulando agrupación lambda...")
        old_values = [c['old_value'] for c in cnombre_changes]
        if old_values:
            grouped_old = ', '.join([str(x) for x in old_values])
            print(f"Resultado agrupación: '{grouped_old}'")
            
            if 'None' in grouped_old:
                print(f"🚨 ¡AQUÍ ESTÁ EL PROBLEMA! Se detectó 'None' en la agrupación")
        else:
            print(f"⚠️ No se generaron cambios CNOMBRE para este registro")
            
        # Analizar también todos los cambios para ver qué campos se detectan
        print(f"\n📋 Todos los cambios detectados:")
        for change in changes:
            print(f"  {change['field']}: '{change['old_value']}' → '{change['new_value']}'")
            
        # Forzar análisis de firstName/lastName específicamente
        print(f"\n🔬 Análisis específico firstName/lastName:")
        profile_details_old = old_data.get('profileDetails', {})
        profile_details_new = new_data.get('profileDetails', {}) if new_data else {}
        
        # Manejar listas
        if isinstance(profile_details_old, list) and profile_details_old:
            profile_details_old = profile_details_old[0]
        if isinstance(profile_details_new, list) and profile_details_new:
            profile_details_new = profile_details_new[0]
            
        # Si no son diccionarios, usar diccionarios vacíos
        if not isinstance(profile_details_old, dict):
            profile_details_old = {}
        if not isinstance(profile_details_new, dict):
            profile_details_new = {}
        
        firstName_old = profile_details_old.get('firstName')
        lastName_old = profile_details_old.get('lastName')
        firstName_new = profile_details_new.get('firstName')
        lastName_new = profile_details_new.get('lastName')
        
        print(f"  firstName: '{firstName_old}' → '{firstName_new}' (cambió: {firstName_old != firstName_new})")
        print(f"  lastName: '{lastName_old}' → '{lastName_new}' (cambió: {lastName_old != lastName_new})")
        
        if firstName_old != firstName_new or lastName_old != lastName_new:
            print(f"  ✅ Debería generar cambios CNOMBRE")
        else:
            print(f"  ❌ No debería generar cambios CNOMBRE")
            
        print(f"  firstName_old type: {type(firstName_old)}")
        print(f"  lastName_old type: {type(lastName_old)}")
        
        if firstName_old is None:
            print(f"  🚨 firstName_old es None!")
        if lastName_old is None:
            print(f"  🚨 lastName_old es None!")

if __name__ == "__main__":
    debug_none_values()
