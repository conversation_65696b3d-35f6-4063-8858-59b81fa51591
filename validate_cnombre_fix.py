#!/usr/bin/env python3
"""
Script para validar que las correcciones de CNOMBRE están funcionando correctamente.
Simula el procesamiento con un archivo pequeño para verificar que los nombres
se están extrayendo y asignando correctamente a las columnas 20 y 21.
"""

import pandas as pd
import json
import re
from datetime import datetime
import sys

def extract_json_changes_corrected(df):
    """
    Versión corregida de extract_json_changes que preserva records CNOMBRE
    """
    json_changes = []
    
    for _, row in df.iterrows():
        try:
            campo = row.get('campo', '')
            old_value_raw = row.get('old_value', '')
            new_value_raw = row.get('new_value', '')
            
            # Convertir a string si no lo son
            old_val = str(old_value_raw) if old_value_raw is not None else ''
            new_val = str(new_value_raw) if new_value_raw is not None else ''
            
            # CORRECCIÓN: No filtrar CNOMBRE records cuando old_val == new_val
            if campo != 'CNOMBRE' and old_val == new_val:
                continue
                
            # Para CNOMBRE, siempre procesar
            if campo == 'CNOMBRE':
                print(f"Procesando CNOMBRE: old_value='{old_val}', new_value='{new_val}'")
                
            json_changes.append({
                'idusuario': row.get('idusuario', ''),
                'campo': campo,
                'old_value': old_val,
                'new_value': new_val,
                'fecha_cambio': row.get('fecha_cambio', '')
            })
            
        except Exception as e:
            print(f"Error procesando fila: {e}")
            continue
    
    return pd.DataFrame(json_changes)

def assign_to_column_exact_corrected(df):
    """
    Versión corregida de assign_to_column_exact que maneja correctamente
    la separación de nombres concatenados con " / "
    """
    result_rows = []
    
    for _, row in df.iterrows():
        try:
            campo = row.get('campo', '')
            old_value = row.get('old_value', '')
            
            # Inicializar columnas
            nnombre = ''
            napellido = ''
            
            if campo == 'CNOMBRE' and old_value:
                old_value_str = str(old_value).strip()
                print(f"Procesando nombre: '{old_value_str}'")
                
                # CORRECCIÓN: Separar por " / " en lugar de espacio
                if ' / ' in old_value_str:
                    parts = old_value_str.split(' / ', 1)
                    nnombre = parts[0].strip() if len(parts) > 0 else ''
                    napellido = parts[1].strip() if len(parts) > 1 else ''
                    print(f"  Separado por ' / ': nombre='{nnombre}', apellido='{napellido}'")
                else:
                    # Si no hay separador, usar todo como nombre
                    nnombre = old_value_str
                    napellido = ''
                    print(f"  Sin separador: nombre='{nnombre}', apellido='{napellido}'")
            
            # Crear fila de resultado
            result_row = {
                'idusuario': row.get('idusuario', ''),
                'campo': campo,
                'old_value': old_value,
                'new_value': row.get('new_value', ''),
                'fecha_cambio': row.get('fecha_cambio', ''),
                '20-NNombre': nnombre,
                '21-NApellido': napellido
            }
            
            result_rows.append(result_row)
            
        except Exception as e:
            print(f"Error asignando columnas: {e}")
            continue
    
    return pd.DataFrame(result_rows)

def create_test_data():
    """
    Crear datos de prueba que simulan el problema original
    """
    test_data = [
        {
            'idusuario': 'USR001',
            'campo': 'CNOMBRE',
            'old_value': 'SANDY ROSA PALACIOS / MEDRANO',
            'new_value': 'SANDY ROSA PALACIOS / MEDRANO',
            'fecha_cambio': '2025-06-13'
        },
        {
            'idusuario': 'USR002', 
            'campo': 'CNOMBRE',
            'old_value': 'MARIA ELENA RODRIGUEZ / SANTOS',
            'new_value': 'MARIA ELENA RODRIGUEZ / SANTOS',
            'fecha_cambio': '2025-06-13'
        },
        {
            'idusuario': 'USR003',
            'campo': 'CNOMBRE', 
            'old_value': 'JUAN CARLOS LOPEZ',
            'new_value': 'JUAN CARLOS LOPEZ',
            'fecha_cambio': '2025-06-13'
        },
        {
            'idusuario': 'USR004',
            'campo': 'CEMAIL',
            'old_value': '<EMAIL>',
            'new_value': '<EMAIL>', 
            'fecha_cambio': '2025-06-13'
        }
    ]
    
    return pd.DataFrame(test_data)

def main():
    print("=== VALIDACIÓN DE CORRECCIONES CNOMBRE ===")
    print(f"Fecha: {datetime.now()}")
    print()
    
    # Crear datos de prueba
    print("1. Creando datos de prueba...")
    test_df = create_test_data()
    print(f"   Creados {len(test_df)} registros de prueba")
    print()
    
    # Aplicar función corregida extract_json_changes
    print("2. Aplicando extract_json_changes corregida...")
    json_changes_df = extract_json_changes_corrected(test_df)
    print(f"   Resultado: {len(json_changes_df)} registros procesados")
    
    # Mostrar registros CNOMBRE procesados
    cnombre_records = json_changes_df[json_changes_df['campo'] == 'CNOMBRE']
    print(f"   Registros CNOMBRE encontrados: {len(cnombre_records)}")
    print()
    
    # Aplicar función corregida assign_to_column_exact
    print("3. Aplicando assign_to_column_exact corregida...")
    final_df = assign_to_column_exact_corrected(json_changes_df)
    print()
    
    # Validar resultados
    print("4. VALIDACIÓN DE RESULTADOS:")
    print("=" * 50)
    
    cnombre_final = final_df[final_df['campo'] == 'CNOMBRE']
    
    for _, row in cnombre_final.iterrows():
        print(f"Usuario: {row['idusuario']}")
        print(f"  Valor original: '{row['old_value']}'")
        print(f"  20-NNombre: '{row['20-NNombre']}'")
        print(f"  21-NApellido: '{row['21-NApellido']}'")
        
        # Validar que no estén vacíos
        if not row['20-NNombre']:
            print(f"  ❌ ERROR: Nombre vacío para usuario {row['idusuario']}")
        else:
            print(f"  ✅ OK: Nombre extraído correctamente")
        print()
    
    # Resumen
    total_cnombre = len(cnombre_final)
    nombres_vacios = len(cnombre_final[cnombre_final['20-NNombre'] == ''])
    
    print("RESUMEN:")
    print(f"  Total registros CNOMBRE: {total_cnombre}")
    print(f"  Nombres vacíos: {nombres_vacios}")
    print(f"  Nombres correctos: {total_cnombre - nombres_vacios}")
    
    if nombres_vacios == 0:
        print("  ✅ ÉXITO: Todas las correcciones funcionan correctamente")
        return True
    else:
        print("  ❌ ERROR: Aún hay nombres vacíos")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
